package com.yxt.lotprice.service.model.dto.b.request;

import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/5/22 16:32
 */
@Data
@ApiModel("去设置页面参数")
public class ToEditPageReq extends PageBase {
    @ApiModelProperty("配置编码")
    private String code;

    @ApiModelProperty("项目属性：GROUP-集团，COMPANY-子公司")
    @NotNull
    private String scopeName;

    @ApiModelProperty("分公司编码")
    @NotNull
    private String companyCode;

    @ApiModelProperty("组织编码")
    private String orCode;

    @ApiModelProperty("职能类别")
    private String roleType;

}
