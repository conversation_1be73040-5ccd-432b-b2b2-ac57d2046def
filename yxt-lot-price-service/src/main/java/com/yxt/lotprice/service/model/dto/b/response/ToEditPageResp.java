package com.yxt.lotprice.service.model.dto.b.response;

import com.yxt.lotprice.service.model.enums.AdjustRoleType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22 16:41
 */
@Data
@ApiModel("去设置页面返回参数")
public class ToEditPageResp {
    @ApiModelProperty("分公司编码")
    private String companyCode;

    @ApiModelProperty("所属子公司名称")
    private String companyName;

    @ApiModelProperty("分部编码")
    private String branchCode;

    @ApiModelProperty("分部名称")
    private String branchName;

    @ApiModelProperty("组织编码")
    private String orCode;

    @ApiModelProperty("组织名称")
    private String orName;

    @ApiModelProperty("用户ID")
    private List<UserDTO> users;

    @ApiModelProperty("报批人类型")
    private AdjustRoleType roleType;
}
