package com.yxt.lotprice.service.model.dto.b.response;

import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/27 11:36
 */
@Data
@ApiModel("调价单商品信息")
public class PriceAdjustFormItemResp {

    @ApiModelProperty("公司编码")
    private String orCode;
    @ApiModelProperty("公司名称")
    private String orName;
    @ApiModelProperty("商品编码")
    private String erpCode;
    @ApiModelProperty("商品名称")
    private String erpName;
    @ApiModelProperty("规格型号")
    private String spec;
    @ApiModelProperty("生产厂家")
    private String manufacture;
    @ApiModelProperty("基本单位")
    private String unit;
    @ApiModelProperty("中标价")
    private BigDecimal bidPrice;
    @ApiModelProperty("集采价格")
    private BigDecimal collectPrice;
    @ApiModelProperty("国谈价格")
    private BigDecimal countryPrice;
    @ApiModelProperty("市场竞对价格")
    private String marketPrice;
    @ApiModelProperty("价格类型")
    private PriceTypeEnum priceType;
    @ApiModelProperty("申请价格")
    private BigDecimal requestPrice;
    @ApiModelProperty("申请调/定价生效开始时间")
    private LocalDate requestStartTime;
    @ApiModelProperty("申请调/定价生效结束时间")
    private LocalDate requestEndTime;
    @ApiModelProperty("审批通过价格")
    private BigDecimal aporovalPrice;
    @ApiModelProperty("审批通过调/定价开始时间")
    private LocalDateTime aporovalStartTime;
    @ApiModelProperty("审批通过调/定价结束时间")
    private LocalDateTime aporovalEndTime;
    @ApiModelProperty("审批备注")
    private String descs;
    @ApiModelProperty("审批结果 1:同意 0:不同意")
    private Integer aporovalStatus;
}
