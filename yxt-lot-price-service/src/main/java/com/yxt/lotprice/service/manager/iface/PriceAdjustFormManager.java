package com.yxt.lotprice.service.manager.iface;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormBO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormSearchBO;
import com.yxt.lotprice.service.model.dto.b.request.InvalidAdjustFormReq;
import com.yxt.lotprice.service.model.dto.b.request.PriceAdjustFormSaveReq;

/**
 * <AUTHOR>
 * @date 2025/5/26 16:01
 */
public interface PriceAdjustFormManager {

    String savePriceAdjustForm(PriceAdjustFormSaveReq req, String userName,String userId);

    Boolean updatePriceAdjustForm(PriceAdjustFormSaveReq req, String userName,String userId);

    PageDTO<PriceAdjustFormBO> page(PriceAdjustFormSearchBO searchBO);

    PriceAdjustFormBO getPriceAdjustFormByCode(String code);

    Boolean invalidPriceAdjustForm(InvalidAdjustFormReq req, String userName, String userId);

    PageDTO<PriceAdjustFormBO> pageJoinAuth(PriceAdjustFormSearchBO bo);
}
