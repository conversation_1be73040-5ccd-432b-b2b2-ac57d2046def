package com.yxt.lotprice.service.model.dto.b.response;

import com.yxt.lotprice.service.model.dto.b.request.PriceAdjustFormItemDTO;
import com.yxt.lotprice.service.model.enums.AdjustScopeType;
import com.yxt.lotprice.service.model.enums.AdjustType;
import com.yxt.lotprice.service.model.enums.DimensionEnum;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 18:18
 */
@Data
@ApiModel("复制调价单返回")
public class CopyAdjustFormResp {
    @ApiModelProperty("调价单名称")
    private String name;
    @ApiModelProperty("项目属性")
    private AdjustScopeType scope;
    @ApiModelProperty("定价/调价维度 STORE-门店,PRICE_GROUP-价格组,COMPANY-子公司")
    private DimensionEnum dimension;
    @ApiModelProperty("定价/调价类别")
    private AdjustType adjustType;
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("价格类型")
    private List<PriceTypeEnum> priceTypes;

    @ApiModelProperty("商品信息")
    private List<PriceAdjustFormItemDTO> items;
}
