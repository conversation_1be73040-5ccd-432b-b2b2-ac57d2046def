package com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase;


import com.yxt.lotprice.service.model.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("调价审批流角色查询")
public class AdjustRoleReq {
    @ApiModelProperty("审批流项目属性-报批人/审批人，APPROVER-审批人，SUBMITTER-报批人")
    AdjustRoleDimensionType roleDimension;

    @ApiModelProperty("审批流范围:COMPANY-公司/GROUP-集团")
    AdjustScopeType scope;

    @ApiModelProperty("查询当前role的信息,名称可通过此接口传入code来查")
    AdjustRoleType roleCode;

}
