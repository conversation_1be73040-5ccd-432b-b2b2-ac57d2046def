package com.yxt.lotprice.service.impl;

import com.google.common.collect.Lists;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lotprice.common.util.CommonConstant;
import com.yxt.lotprice.service.DoaCommodityService;
import com.yxt.lotprice.service.adjustbase.model.bo.PriceAdjustDoaCommodityBO;
import com.yxt.lotprice.service.adjustbase.model.bo.PriceAdjustDoaCommoditySearchBO;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.DoaCommodityRemoveReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.DoaCommoditySaveReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.DoaCommoditySearchReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase.DoaCommoditySearchResp;
import com.yxt.lotprice.service.manager.iface.DoaCommodityManager;
import com.yxt.lotprice.service.utils.BeanUtil;
import com.yxt.merchandise.search.common.model.dto.enums.GoodsType;
import com.yxt.merchandise.search.common.model.dto.request.CommodityBaseInfoBatchReq;
import com.yxt.merchandise.search.common.model.dto.response.CommodityBaseInfoResp;
import com.yxt.merchandise.search.sdk.api.CommodityApi;
import com.yxt.org.read.opensdk.org.dto.request.OrganizationCacheOpenReqDTO;
import com.yxt.org.read.opensdk.org.dto.request.OrganizationConditionOpenReqDTO;
import com.yxt.org.read.opensdk.org.dto.response.OrgInfoQueryOpenResDTO;
import com.yxt.org.read.opensdk.org.service.OrgQueryOpenApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class DoaCommodityServiceImpl implements DoaCommodityService {

    @Value("${doacommodity.max.size:100000}")
    private Integer maxLimitSize;

    @Autowired
    private DoaCommodityManager doaCommodityManager;

    @Autowired
    private OrgQueryOpenApi orgQueryOpenApi;

    @Autowired
    private CommodityApi commodityApi;

    @Override
    public String save(DoaCommoditySaveReq req, String userName) {
        long maxSize = (long) req.getErpCodes().size() * req.getOrgs().size();
        if ((long) req.getErpCodes().size() * req.getOrgs().size() > maxLimitSize){
            throw new YxtBizException("下放商品数量乘以机构"+"当前数量:"+maxSize+",不能超过："+maxLimitSize);
        }

        OrganizationConditionOpenReqDTO orgReq = new OrganizationConditionOpenReqDTO();
        orgReq.setOrgCodeList(new ArrayList<>(req.getOrgs()));
        orgReq.setMerCode(CommonConstant.MERCODE_500001);
        ResponseBase<List<OrgInfoQueryOpenResDTO>> listResponseBase = orgQueryOpenApi.listOrgByCondition(orgReq);
        Map<String, String> orgMap;
        try {
            if(!listResponseBase.checkSuccess()){
                throw new YxtBizException("查询组织机构信息报错，原因："+listResponseBase);
            }
            orgMap = listResponseBase.getData().stream().collect(Collectors.toMap(OrgInfoQueryOpenResDTO::getOrCode, OrgInfoQueryOpenResDTO::getParentIdPath, (v1, v2) -> v1));
        }catch (Exception e){
            log.error("查询组织机构信息报错，原因："+e.getMessage(),e);
            throw new YxtBizException("查询组织机构信息报错，原因："+e.getMessage());
        }
        if (MapUtils.isEmpty(orgMap)){
            throw new YxtBizException("组织机构信息为空！");
        }



        //查询已存在的下放商品清单
        List<PriceAdjustDoaCommodityBO> exists = doaCommodityManager.list(req.getErpCodes(), req.getOrgs());
        Map<String, PriceAdjustDoaCommodityBO> existMap = exists.stream().collect(Collectors.toMap(x -> x.getErpCode() + "_" + x.getOrgCode(), x -> x, (v1, v2) -> v1));


        //构建下放商品清单数据
        List<PriceAdjustDoaCommodityBO> data = new ArrayList<>();
        req.getOrgs().stream().forEach(orgCode -> {
            req.getErpCodes().forEach(erpCode -> {
                PriceAdjustDoaCommodityBO bo = existMap.get(erpCode + "_" + orgCode);
                if (bo == null){
                    bo = new PriceAdjustDoaCommodityBO();
                    bo.setCreateName(userName);
                }
                bo.setOrgCode(orgCode);
                bo.setErpCode(erpCode);
                bo.setOrgPath(orgMap.get(orgCode));
                bo.setModifyName(userName);
                bo.setDescs(req.getDescs());
                bo.setIsValid(1);
                data.add(bo);
            });
            if (StringUtils.isBlank(orgMap.get(orgCode))){
                log.error("orgCode:{},orgPath:{}",orgCode,orgMap.get(orgCode));
            }
        });
        doaCommodityManager.batchSave(data);
        return "success";
    }

    @Override
    public PageDTO<DoaCommoditySearchResp> page(DoaCommoditySearchReq req) {
        PriceAdjustDoaCommoditySearchBO bo = new PriceAdjustDoaCommoditySearchBO();
        bo.setErpCodes(req.getErpCodes());
        bo.setOrgs(req.getOrgs());
        bo.setIsValid(1);
        bo.setCurrentPage(req.getCurrentPage());
        bo.setPageSize(req.getPageSize());
        bo.setCommodityName(req.getCommodityName());
        PageDTO<PriceAdjustDoaCommodityBO> pageData = doaCommodityManager.page(bo);

        PageDTO<DoaCommoditySearchResp> result = new PageDTO<>();
        result.setTotalCount(pageData.getTotalCount());
        result.setTotalPage(pageData.getTotalPage());
        result.setCurrentPage(pageData.getCurrentPage());
        result.setPageSize(pageData.getPageSize());
        result.setData(BeanUtil.copyList(pageData.getData(), DoaCommoditySearchResp.class));

        Map<String, List<DoaCommoditySearchResp>> map = result.getData().stream().collect(Collectors.groupingBy(x -> x.getErpCode()));
        List<String> erpCodes = Lists.newArrayList(map.keySet());
        CommodityBaseInfoBatchReq cBaseReq = new CommodityBaseInfoBatchReq();
        try {
            Map<String, String> erpCodeNameMap = Lists.partition(erpCodes, 1000).stream().map(partition -> {
                try {
                    cBaseReq.setErpCodes(erpCodes);
                    cBaseReq.setGoodsType(GoodsType.SOURCE);
                    ResponseBase<List<CommodityBaseInfoResp>> commodityResp = commodityApi.batchQueryBaseInfo(cBaseReq);
                    if (commodityResp.checkSuccess()) {
                        return commodityResp.getData();
                    }
                    throw new YxtBizException(commodityResp.toString());
                }catch (Exception e) {
                    log.warn("获取商品基础信息报错！", e);
                }
                return null;
            }).filter(Objects::nonNull).flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toMap(CommodityBaseInfoResp::getErpCode, CommodityBaseInfoResp::getName, (V1, V2) -> V1));

            map.forEach((k,v)->{
                String name = erpCodeNameMap.get(k);
                if (StringUtils.isNotBlank(name)){
                    v.forEach(item-> item.setCommodityName(name));
                }
            });
        }catch (Exception e){
            log.warn("获取商品基础信息报错2！", e);
        }
        return result;
    }

    @Override
    public Long remove(DoaCommodityRemoveReq req) {
        PriceAdjustDoaCommoditySearchBO bo = new PriceAdjustDoaCommoditySearchBO();
        bo.setErpCodes(req.getErpCodes());
        bo.setOrgs(req.getOrgs());
        bo.setIds(req.getIds());
        bo.setCommodityName(req.getCommodityName());
        return doaCommodityManager.batchRemove(bo);
    }
}
