package com.yxt.lotprice.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lotprice.service.AdjustFormAuthService;
import com.yxt.lotprice.service.DoaCommodityService;
import com.yxt.lotprice.service.PriceAdjustFormService;
import com.yxt.lotprice.service.UserService;
import com.yxt.lotprice.service.adjustbase.model.bo.*;
import com.yxt.lotprice.service.cache.AllUserInfoCache;
import com.yxt.lotprice.service.cache.OrgInfoCacheUtils;
import com.yxt.lotprice.service.manager.iface.*;
import com.yxt.lotprice.service.model.bo.*;
import com.yxt.lotprice.service.model.dto.b.request.AdjustFormPageReq;
import com.yxt.lotprice.service.model.dto.b.request.InvalidAdjustFormReq;
import com.yxt.lotprice.service.model.dto.b.request.PriceAdjustFormSaveReq;
import com.yxt.lotprice.service.model.dto.b.response.AdjustFormInfoResp;
import com.yxt.lotprice.service.model.dto.b.response.AdjustFormPageResp;
import com.yxt.lotprice.service.model.dto.b.response.PriceAdjustFormItemResp;
import com.yxt.lotprice.service.model.dto.b.request.*;
import com.yxt.lotprice.service.model.dto.b.response.*;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationDTO;
import com.yxt.lotprice.service.model.enums.*;
import com.yxt.lotprice.service.model.enums.AdjustFormAuditStatus;
import com.yxt.lotprice.service.model.enums.CancelStatusEnum;
import com.yxt.lotprice.service.utils.ExcelUtil;
import com.yxt.org.read.opensdk.emp.dto.response.EmployeeScrollResDTO;
import com.yxt.org.read.opensdk.emp.dto.response.EmployeeScrollResDTO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/26 15:07
 */
@Service
@Slf4j
public class PriceAdjustFormServiceImpl implements PriceAdjustFormService {

    @Resource
    private PriceAdjustFormManager priceAdjustFormManager;

    @Autowired
    AdjustFormAuthService adjustFormAuthService;

    @Autowired
    ApprovalFlowManager approvalFlowManager;

    @Resource
    private PriceAdjustFormItemManager priceAdjustFormItemManager;

    @Autowired
    private DoaCommodityManager doaCommodityManager;

    @Resource
    private ApprovalFlowTaskManager approvalFlowTaskManager;
    @Autowired
    private UserService userService;

    @Autowired
    private AllUserInfoCache userInfoCache;

    @Override
    public Boolean savePriceAdjustForm(PriceAdjustFormSaveReq req, String userName,String userId) {
        if (CollUtil.isEmpty(req.getItems())){
            return false;
        }
        List<PriceAdjustFormSaveReq> formList = splitFormAndLocateApprovalDefinition(req, userId);

        SpringUtil.getBean(PriceAdjustFormService.class).saveFormBatch(userName, userId, formList);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFormBatch(String userName, String userId, List<PriceAdjustFormSaveReq> formList) {
        formList.forEach(form -> {
            if (form.getCode() == null){
                String code = priceAdjustFormManager.savePriceAdjustForm(form, userName, userId);
                form.setCode(code);
                priceAdjustFormItemManager.savePriceAdjustFormItem(form, userName);
            }else {
                priceAdjustFormManager.updatePriceAdjustForm(form, userName, userId);
                priceAdjustFormItemManager.updatePriceAdjustFormItem(form, userName);
            }
        });
    }

    @Override
    public PageDTO<AdjustFormPageResp> page(AdjustFormPageReq req, String userId){
        PageDTO<AdjustFormPageResp> result = new PageDTO<>();
        Integer tabType = req.getTabType();
        //todo,员工所属机构的机构路径
        if (tabType == null){
            log.error("tabType为空！");
            //todo 打印日志
        }else if(tabType == 1){//我发起的
            result = pageICreate(req, userId);
        }else if (tabType == 2){//待我处理的
            result = pageIApprove(req, userId);
        }else if (tabType == 3){//我关注的（我有权限查看的）
            result = pageIFoucs(req, userId);
        }else {
            log.error("tabType值不正确，只能是1,2,3！");
        }
        return result;
    }

    @Override
    public PageDTO<AdjustFormPageResp> pageICreate(AdjustFormPageReq req, String userId) {
        PriceAdjustFormSearchBO bo = new PriceAdjustFormSearchBO();
        BeanUtil.copyProperties(req, bo);
        bo.setCreateUserId(userId);
        PageDTO<PriceAdjustFormBO> page = priceAdjustFormManager.page(bo);
        return wrap(page);
    }

    @Override
    public PageDTO<AdjustFormPageResp> pageIApprove(AdjustFormPageReq req, String userId) {
        List<PriceAdjustFormSearchAuthFinalBO> authList = adjustFormAuthService.fillIApproveAuthData(userId);
        PriceAdjustFormSearchBO bo = new PriceAdjustFormSearchBO();
        BeanUtil.copyProperties(req, bo);
        bo.setAuthData(authList);
        PageDTO<PriceAdjustFormBO> page = priceAdjustFormManager.pageJoinAuth(bo);
        return wrap(page);
    }

    @Override
    public PageDTO<AdjustFormPageResp> pageIFoucs(AdjustFormPageReq req, String userId) {

        List<PriceAdjustFormSearchAuthFinalBO> authList = adjustFormAuthService.fillIFoucsAuthData(userId);
        PriceAdjustFormSearchBO bo = new PriceAdjustFormSearchBO();
        BeanUtil.copyProperties(req, bo);
        bo.setAuthData(authList);
        PageDTO<PriceAdjustFormBO> page = priceAdjustFormManager.pageJoinAuth(bo);
        return wrap(page);
    }

    private PageDTO<AdjustFormPageResp> wrap(PageDTO<PriceAdjustFormBO> page){
        PageDTO<AdjustFormPageResp> resultData = new PageDTO<>();
        resultData.setPageSize(page.getPageSize());
        resultData.setCurrentPage(page.getCurrentPage());
        resultData.setTotalCount(page.getTotalCount());
        resultData.setTotalPage(page.getTotalPage());
        resultData.setData(BeanUtil.copyToList(page.getData(), AdjustFormPageResp.class));
        resultData.getData().forEach(item -> {
            item.setEditFlag(item.getStatus() == AdjustFormAuditStatus.DRAFT ?  1 : 0);
            item.setCopyFlag(1);
            item.setRejectFlag(0);
            item.setAuditFlag(item.getRejectFlag());
            item.setCancelFlag(item.getStatus() == AdjustFormAuditStatus.APPROVED ?  1 : 0);
            item.setInvalidFlag(item.getStatus() == AdjustFormAuditStatus.SUBMITTED ?  1 : 0);
            item.setStatusName(item.getStatus().getNameCh());
            item.setScopeName(item.getScope().getNameCh());
            item.setDimensionName(item.getDimension().getNameCh());
            item.setCreateRoleName(item.getCreateUserRole().getNameCh());
        });
        return resultData;
    }


    @Override
    public Boolean invalidPriceAdjustForm(InvalidAdjustFormReq req, String userName, String userId) {

        PriceAdjustFormBO priceAdjustFormBO = priceAdjustFormManager.getPriceAdjustFormByCode(req.getCode());
        if (priceAdjustFormBO == null){
            throw new YxtBizException("调价单不存在");
        }
        if (priceAdjustFormBO.getCreateUserId() != Integer.parseInt(userId)){
            throw new YxtBizException("非本人创建调价单,不可作废");
        }
        if (!AdjustFormAuditStatus.APPROVED.equals(priceAdjustFormBO.getStatus())){
            throw new YxtBizException("未审核完成,不可作废");
        }
        if (CancelStatusEnum.FULLY_CANCELED.equals(priceAdjustFormBO.getCancelStatus())){
            throw new YxtBizException("已整单作废");
        }

        return priceAdjustFormManager.invalidPriceAdjustForm(req, userName, userId);
    }

    @Override
    public AdjustFormInfoResp getAdjustFormInfo(String code, String userName, String userId) {
        //获取调价单基本信息
        PriceAdjustFormBO priceAdjustFormBO = priceAdjustFormManager.getPriceAdjustFormByCode(code);
        if (priceAdjustFormBO == null){
            throw new YxtBizException("调价单不存在");
        }
        AdjustFormInfoResp resp = new AdjustFormInfoResp();
        resp.setCode(code);
        resp.setName(priceAdjustFormBO.getName());
        resp.setScope(priceAdjustFormBO.getScope());
        resp.setDimension(priceAdjustFormBO.getDimension());
        resp.setAdjustType(priceAdjustFormBO.getPriceOpsType());

        //获取商品信息
        List<PriceAdjustFormItemResp> items = priceAdjustFormItemManager.getPriceAdjustFormItem(priceAdjustFormBO.getCode());
        resp.setItems(items);

        String flowDefinition = priceAdjustFormBO.getFlowDefinition();
        ApprovalFlowDefinitionBO approvalFlowDefinitionBO = JSON.parseObject(flowDefinition, ApprovalFlowDefinitionBO.class);
        List<ApprovalFlowDefinitionBO.ApprovalFlowNodeItemBO> configNodeList = approvalFlowDefinitionBO.getConfigNodeList();

        //获取审批信息
        List<PriceAdjustAporovalTaskBO> aporovalTaskBOS = approvalFlowTaskManager.getApprovalTaskByFormId(priceAdjustFormBO.getId(), priceAdjustFormBO.getFlowDefinitionId());

        List<AdjustFormAporovalTaskResp> flowInfos = new ArrayList<>();
        List<AdjustFormAporovalTaskResp> cancelInfos = new ArrayList<>();

        List<PriceAdjustAporovalTaskBO> filteredAporovalTaskBOS = new ArrayList<>();
        for (PriceAdjustAporovalTaskBO aporovalTaskBO : aporovalTaskBOS) {
            if (AdjustFormAuditStatus.SUBMITTED.name().equals(aporovalTaskBO.getStatus())){
                AdjustFormAporovalTaskResp adjustFormAporovalTaskResp = new AdjustFormAporovalTaskResp();
                adjustFormAporovalTaskResp.setCurrentNode("定/调价申请");
                adjustFormAporovalTaskResp.setUserName(aporovalTaskBO.getCreateName());
                adjustFormAporovalTaskResp.setOperateTime(aporovalTaskBO.getCreateTime());
                adjustFormAporovalTaskResp.setDescs(aporovalTaskBO.getDescs());
                flowInfos.add(adjustFormAporovalTaskResp);
                continue;
            }
            if (AdjustFormAuditStatus.CANCELED.name().equals(aporovalTaskBO.getStatus())){
                AdjustFormAporovalTaskResp revokeTask = new AdjustFormAporovalTaskResp();
                revokeTask.setUserName(aporovalTaskBO.getCreateName());
                revokeTask.setOperateTime(aporovalTaskBO.getCreateTime());
                revokeTask.setDescs(aporovalTaskBO.getDescs());
                resp.setRevokeInfo(revokeTask);
                continue;
            }
            if (!CancelStatusEnum.NOT_CANCELED.name().equals(aporovalTaskBO.getCancelStatus())){
                AdjustFormAporovalTaskResp taskResp = new AdjustFormAporovalTaskResp();
                taskResp.setUserName(aporovalTaskBO.getCreateName());
                taskResp.setOperateTime(aporovalTaskBO.getCreateTime());
                taskResp.setDescs(aporovalTaskBO.getDescs());
                taskResp.setCancelStatus(CancelStatusEnum.valueOf(aporovalTaskBO.getCancelStatus()));
                cancelInfos.add(taskResp);
                continue;
            }
            filteredAporovalTaskBOS.add(aporovalTaskBO);
        }
        for (int i = 0; i < configNodeList.size(); i++){
            ApprovalFlowDefinitionBO.ApprovalFlowNodeItemBO approvalFlowNodeItemBO = configNodeList.get(i);
            AdjustFormAporovalTaskResp adjustFormAporovalTaskResp = new AdjustFormAporovalTaskResp();
            adjustFormAporovalTaskResp.setCurrentNode(approvalFlowNodeItemBO.getRoleCode().getNameCh());
            adjustFormAporovalTaskResp.setUserName(approvalFlowNodeItemBO.getName());
            if (i < filteredAporovalTaskBOS.size()){
                PriceAdjustAporovalTaskBO aporovalTaskBO = filteredAporovalTaskBOS.get(i);
                adjustFormAporovalTaskResp.setOperateTime(aporovalTaskBO.getCreateTime());
                adjustFormAporovalTaskResp.setDescs(aporovalTaskBO.getDescs());
            }

            flowInfos.add(adjustFormAporovalTaskResp);
        }
        resp.setFlowInfos(flowInfos);
        resp.setCancelInfos(cancelInfos);

        return resp;
    }


    @Override
    public void importCompanyPrice(MultipartFile multipartFile, String userName, String userId) {
        List<PriceAdjustFormImportCompanyBO> importDatalist = ExcelUtil.readSheet(multipartFile,0,PriceAdjustFormImportCompanyBO.class);
        List<PriceAdjustFormImportPublicDataBO> publicData = ExcelUtil.readSheet(multipartFile,1,PriceAdjustFormImportPublicDataBO.class);

        List<PriceAdjustFormImportBaseBO> baseList = BeanUtil.copyToList(importDatalist, PriceAdjustFormImportBaseBO.class);
        // 数据校验
        importDataVerify(baseList,publicData);
        for (PriceAdjustFormImportCompanyBO companyBO : importDatalist){
            if (StringUtils.isEmpty(companyBO.getOrgCode())){
                throw new YxtBizException("公司编码不能为空");
            }
            PriceAdjustFormItemDTO item = new PriceAdjustFormItemDTO();
            PriceAdjustFormSaveReq req = importDataConvert(companyBO, publicData,item,userId);
            req.setDimension(DimensionEnum.COMPANY);    //定调价维度 -- 公司
            req.getItems().get(0).setOrgCode(companyBO.getOrgCode());
            item.setOrgCode(companyBO.getOrgCode());    // 公司编码
            item.setOrgName(companyBO.getOrgName());    // 公司名称
            savePriceAdjustForm(req,userName,userId);
        }
    }

    @Override
    public void importGroupPrice(MultipartFile multipartFile, String userName, String userId) {
        List<PriceAdjustFormImportGroupBO> importDatalist = ExcelUtil.readSheet(multipartFile,0,PriceAdjustFormImportGroupBO.class);
        List<PriceAdjustFormImportPublicDataBO> publicData = ExcelUtil.readSheet(multipartFile,1,PriceAdjustFormImportPublicDataBO.class);

        List<PriceAdjustFormImportBaseBO> baseList = BeanUtil.copyToList(importDatalist, PriceAdjustFormImportBaseBO.class);
        // 数据校验
        importDataVerify(baseList,publicData);
        for (PriceAdjustFormImportGroupBO groupBO : importDatalist){
            if (StringUtils.isEmpty(groupBO.getOrgCode())){
                throw new YxtBizException("价格组编码不能为空");
            }
            PriceAdjustFormItemDTO item = new PriceAdjustFormItemDTO();
            PriceAdjustFormSaveReq req = importDataConvert(groupBO, publicData,item,userId);
            req.setDimension(DimensionEnum.PRICE_GROUP);    //定调价维度 -- 价格组
            req.getItems().get(0).setOrgCode(groupBO.getOrgCode());
            item.setOrgCode(groupBO.getOrgCode());
            item.setOrgName(groupBO.getOrgName());
            savePriceAdjustForm(req,userName,userId);
        }
    }
    @Override
    public void importStorePrice(MultipartFile multipartFile, String userName, String userId) {
        List<PriceAdjustFormImportStoreBO> importDatalist = ExcelUtil.readSheet(multipartFile,0,PriceAdjustFormImportStoreBO.class);
        List<PriceAdjustFormImportPublicDataBO> publicData = ExcelUtil.readSheet(multipartFile,1,PriceAdjustFormImportPublicDataBO.class);

        List<PriceAdjustFormImportBaseBO> baseList = BeanUtil.copyToList(importDatalist, PriceAdjustFormImportBaseBO.class);
        // 数据校验
        importDataVerify(baseList,publicData);
        for (PriceAdjustFormImportStoreBO storeBO : importDatalist){
            if (StringUtils.isEmpty(storeBO.getOrgCode())){
                throw new YxtBizException("门店编码不能为空");
            }
            PriceAdjustFormItemDTO item = new PriceAdjustFormItemDTO();
            PriceAdjustFormSaveReq req = importDataConvert(storeBO, publicData,item,userId);
            req.setDimension(DimensionEnum.PRICE_GROUP);    //定调价维度 -- 门店
            req.getItems().get(0).setOrgCode(storeBO.getOrgCode());
            item.setOrgCode(storeBO.getOrgCode());
            item.setOrgName(storeBO.getOrgName());
            savePriceAdjustForm(req,userName,userId);
        }
    }

    @Override
    public CopyAdjustFormResp copyAdjustForm(CopyAdjustFormReq req, String userId) {
        //获取调价单基本信息
        PriceAdjustFormBO priceAdjustFormBO = priceAdjustFormManager.getPriceAdjustFormByCode(req.getCode());
        if (priceAdjustFormBO == null){
            throw new YxtBizException("调价单不存在");
        }
        if (Integer.parseInt(userId) != priceAdjustFormBO.getCreateUserId()){
            throw new YxtBizException("非本人发起的调价单,不可复制");
        }
        AdjustRoleType userRoleType = userService.getUserRoleType(userId);
        if (userRoleType == null){
            throw new YxtBizException("未找到用户角色");
        }
        //获取当前用户的调价范围
        PriceAdjustRoleConfigBO userRoleConfig = userService.getUserRoleConfig(userId, userRoleType);
        if (userRoleConfig == null){
            throw new YxtBizException("未找到用户调价范围权限");
        }
        CopyAdjustFormResp copyAdjustFormResp = new CopyAdjustFormResp();
        copyAdjustFormResp.setName(priceAdjustFormBO.getName());
        copyAdjustFormResp.setScope(userRoleType.getScope());
        copyAdjustFormResp.setDimension(priceAdjustFormBO.getDimension());
        copyAdjustFormResp.setRemark(priceAdjustFormBO.getRemark());
        if (Arrays.asList(userRoleConfig.getPriceOpsType().split(",")).contains(priceAdjustFormBO.getPriceOpsType().name())){
            copyAdjustFormResp.setAdjustType(priceAdjustFormBO.getPriceOpsType());
        }
        List<PriceTypeEnum> priceTypeEnumList = Arrays.stream(userRoleConfig.getPriceOpsType().split(",")).map(PriceTypeEnum::valueOf).collect(Collectors.toList());
        copyAdjustFormResp.setPriceTypes(priceTypeEnumList);
        //获取商品信息
        List<PriceAdjustFormItemResp> items = priceAdjustFormItemManager.getPriceAdjustFormItem(priceAdjustFormBO.getCode());
        Map<String, List<PriceAdjustFormItemResp>> map = items.stream().collect(Collectors.groupingBy(PriceAdjustFormItemResp::getErpCode));
        List<PriceAdjustFormItemDTO> itemDTOList = new ArrayList<>();
        for (Map.Entry<String, List<PriceAdjustFormItemResp>> entry : map.entrySet()) {
            PriceAdjustFormItemDTO itemDTO = new PriceAdjustFormItemDTO();
            BeanUtils.copyProperties(entry.getValue().get(0),itemDTO);
            for (PriceAdjustFormItemResp resp : entry.getValue()) {
                switch (resp.getPriceType()){
                    case RETAIL:
                        itemDTO.setRetailPrice(resp.getRequestPrice());
                        itemDTO.setRetailPriceBeginTime(resp.getRequestStartTime());
                        itemDTO.setRetailPriceEndTime(resp.getRequestEndTime());
                        break;
                    case VIP:
                        itemDTO.setVipPrice(resp.getRequestPrice());
                        itemDTO.setVipPriceBeginTime(resp.getRequestStartTime());
                        itemDTO.setVipPriceEndTime(resp.getRequestEndTime());
                        break;
                    case CHRONIC:
                        itemDTO.setChronicPrice(resp.getRequestPrice());
                        itemDTO.setChronicPriceBeginTime(resp.getRequestStartTime());
                        itemDTO.setChronicPriceEndTime(resp.getRequestEndTime());
                        break;
                    default:
                        break;
                }
            }
            itemDTOList.add(itemDTO);
        }
        copyAdjustFormResp.setItems(itemDTOList);
        return copyAdjustFormResp;
    }

    @Override
    public ApprovalFlowDefinitionBO locateAdjustFormFlowDefinition(EmployeeScrollResDTO.EmployeeResDTO userInfo, String companyCode, AdjustScopeType scope, AdjustFlowType flowType, AdjustType type) {
        if (StringUtils.isEmpty(companyCode)){
            throw new YxtBizException(String.format("员工【%s】未找到所属子公司/集团！", userInfo.getEmpCode()+userInfo.getEmpName()));
        }

        ApprovalFlowDefinitionReqBO req = new ApprovalFlowDefinitionReqBO();
        req.setCompanyCode(Lists.newArrayList(companyCode));
        req.setFlowType(Lists.newArrayList(flowType));
        req.setAdjustType(Lists.newArrayList(type));
        req.setScope(scope);
        PageDTO<ApprovalFlowDefinitionBO> page = approvalFlowManager.page(req);
        List<ApprovalFlowDefinitionBO> definitions = page.getData();

        if (definitions.isEmpty()){
            throw new YxtBizException(String.format("没有找到审批流！【%s-%s-%s-%s-%s】"
                    , userInfo.getEmpCode()+userInfo.getEmpName(), companyCode, scope.getNameCh(), flowType.getNameCh(), type.getNameCh()));
        }else if (definitions.size() > 1){
            throw new YxtBizException(String.format("找到多个审批流【%s-%s-%s-%s-%s】，审批流编码：%s"
                    , userInfo.getEmpCode()+userInfo.getEmpName(), companyCode, scope.getNameCh(), flowType.getNameCh()
                    , type.getNameCh(),definitions.stream().map(ApprovalFlowDefinitionBO::getCode).collect(Collectors.joining(","))));
        }
        return definitions.get(0);
    }

    @Override
    public List<PriceAdjustFormSaveReq> splitFormAndLocateApprovalDefinition(PriceAdjustFormSaveReq req, String userId) {

        List<PriceAdjustFormSaveReq> reqList = new ArrayList<>();
        reqList.add(req);

        EmployeeScrollResDTO.EmployeeResDTO userInfo = userInfoCache.getUserInfo(userId);
        if (userInfo == null){
            throw new YxtBizException("员工不存在"+userId);
        }
        //查所属角色
        AdjustRoleType userRoleType = userService.getUserRoleType(userId);
        if (userRoleType == null){
            throw new YxtBizException("当前用户【%s】角色信息为空:" + userInfo.getEmpCode()+userInfo.getEmpName());
        }
        PriceAdjustRoleConfigBO userRoleConfig = userService.getUserRoleConfig(userId, userRoleType);
        if (userRoleConfig == null){
            throw new YxtBizException(String.format("当前用户【%s】报批人调价范文配置为空:%s", userInfo.getEmpCode()+userInfo.getEmpName(), userInfo.getSubCompCode()+"-"+userRoleType.getNameCh()));
        }
        List<AdjustFlowType> adjustFlowTypes = userRoleConfig.flowTypeList();
        if (adjustFlowTypes.isEmpty()){
            throw new YxtBizException(String.format("当前用户【%s】审批的长/短流程配置为空:%s", userInfo.getEmpCode()+userInfo.getEmpName(),  userInfo.getSubCompCode()+"-"+userRoleType.getNameCh()));
        }

        if (req.getStatus().equals(AdjustFormAuditStatus.SUBMITTED)){

            //有长短流程才拆分调价单
            List<PriceAdjustFormItemDTO> doaShortList = new ArrayList<>();
            List<PriceAdjustFormItemDTO> longList = new ArrayList<>();
            if (adjustFlowTypes.contains(AdjustFlowType.LONG) && adjustFlowTypes.contains(AdjustFlowType.SHORT)){
                String subOrgCode = userInfo.getSubOrgCode();
                Map<String, List<PriceAdjustFormItemDTO>> erpItemMap = req.getItems().stream().collect(Collectors.groupingBy(PriceAdjustFormItemDTO::getErpCode));
                Set<SysOrganizationDTO> orgAndAllParent = OrgInfoCacheUtils.getOrgAndAllParent(subOrgCode);
                Set<String> orgCodeList = orgAndAllParent.stream().map(SysOrganizationDTO::getOrCode).collect(Collectors.toSet());

                Set<String> doaErpCodes = Lists.partition(new LinkedList<>(erpItemMap.keySet()), 500)
                        .stream()
                        .map(partition ->
                                doaCommodityManager.list(new HashSet<>(partition), orgCodeList)
                                        .stream()
                                        .map(PriceAdjustDoaCommodityBO::getErpCode)
                                        .collect(Collectors.toSet()))
                        .flatMap(Collection::stream).collect(Collectors.toSet());
                erpItemMap.forEach((k,v)->{
                    if (doaErpCodes.contains(k)){
                        doaShortList.addAll(v);
                    }else {
                        longList.addAll(v);
                    }
                });
            }
            if (!doaShortList.isEmpty() && !longList.isEmpty()){//短流程+长流程
                //查找审批流
                ApprovalFlowDefinitionBO shortDefinition = locateAdjustFormFlowDefinition(userInfo, userInfo.getSubCompCode(), req.getScope(), AdjustFlowType.SHORT, req.getAdjustType());
                ApprovalFlowDefinitionBO longDefinition = locateAdjustFormFlowDefinition(userInfo, userInfo.getSubCompCode(), req.getScope(), AdjustFlowType.LONG, req.getAdjustType());

                //短流程赋值
                req.setItems(doaShortList);
                req.setApprovalFlowDefinition(shortDefinition);
                req.setName(req.getName()+"-短流程（系统自动拆分）");

                //长流程赋值
                PriceAdjustFormSaveReq longForm = BeanUtil.copyProperties(req, PriceAdjustFormSaveReq.class);
                longForm.setCode(null);
                longForm.setName(longForm.getName()+"-长流程（系统自动拆分）");
                longForm.setItems(longList);
                longForm.setApprovalFlowDefinition(longDefinition);
                reqList.add(longForm);

            }else if  (!doaShortList.isEmpty()){//仅有短流程
                //查找审批流
                ApprovalFlowDefinitionBO shortDefinition = locateAdjustFormFlowDefinition(userInfo, userInfo.getSubCompCode(), req.getScope(), AdjustFlowType.SHORT, req.getAdjustType());
                //短流程赋值
                req.setItems(doaShortList);
                req.setApprovalFlowDefinition(shortDefinition);
            }else if  (!longList.isEmpty()){//仅有长流程
                ApprovalFlowDefinitionBO longDefinition = locateAdjustFormFlowDefinition(userInfo, userInfo.getSubCompCode(), req.getScope(), AdjustFlowType.LONG, req.getAdjustType());
                //长流程赋值
                req.setItems(longList);
                req.setApprovalFlowDefinition(longDefinition);
            }else {//不需要拆分长短流程
                ApprovalFlowDefinitionBO definition = locateAdjustFormFlowDefinition(userInfo, userInfo.getSubCompCode(), req.getScope(), adjustFlowTypes.get(0), req.getAdjustType());
                req.setApprovalFlowDefinition(definition);
            }
        }
        else {
            //暂存状态不用管
        }
        return reqList;
    }

    @Override
    public CurrentUserAdjustScopeResp getCurrentUserAdjustScope(String userId) {
        AdjustRoleType userRoleType = userService.getUserRoleType(userId);
        if (userRoleType == null){
            throw new YxtBizException("未找到用户角色");
        }
        //获取当前用户的调价范围
        PriceAdjustRoleConfigBO userRoleConfig = userService.getUserRoleConfig(userId, userRoleType);
        if (userRoleConfig == null){
            throw new YxtBizException("未找到用户调价范围权限");
        }
        CurrentUserAdjustScopeResp resp = new CurrentUserAdjustScopeResp();
        if (AdjustRoleType.STORE_S_1.equals(userRoleType)){
            resp.setStore(userService.getUserStore(userId));
        }

        resp.setScope(userRoleType.getScope());
        resp.setAdjustTypeList(Arrays.stream(userRoleConfig.getPriceOpsType().split(",")).map(e->{
            AdjustType adjustType = AdjustType.valueOf(e);
            SelectorDTO selectorDTO = new SelectorDTO();
            selectorDTO.setName(adjustType.getNameCh());
            selectorDTO.setCode(adjustType.name());
            return selectorDTO;
        }).collect(Collectors.toList()));

        resp.setDimensionList(Arrays.stream(userRoleConfig.getDimension().split(",")).map(e->{
            DimensionEnum dimensionEnum = DimensionEnum.valueOf(e);
            SelectorDTO selectorDTO = new SelectorDTO();
            selectorDTO.setName(dimensionEnum.getNameCh());
            selectorDTO.setCode(dimensionEnum.getValue());
            return selectorDTO;
        }).collect(Collectors.toList()));

        resp.setPriceTypeList(Arrays.stream(userRoleConfig.getPriceType().split(",")).map(e->{
            PriceTypeEnum priceTypeEnum = PriceTypeEnum.valueOf(e);
            SelectorDTO selectorDTO = new SelectorDTO();
            selectorDTO.setName(priceTypeEnum.getName());
            selectorDTO.setCode(priceTypeEnum.getValue());
            return selectorDTO;
        }).collect(Collectors.toList()));

        return resp;
    }

    @Override
    public List<CurrentUserCompanyResp> currentUserCompany(String userId) {
        return userService.currentUserCompany(userId);
    }

    @Override
    public List<AdjustFormAporovalLogResp> getAdjustFormAporovalLog(AdjustFormAporovalLogReq req) {
        return priceAdjustFormItemManager.getAdjustFormAporovalLog(req);
    }

    @Override
    public List<SelectorDTO> getAdjustFormEnum(AdjustFormEnumReq req) {
        switch (req.getType()){
            case 1:
                return Arrays.stream(AdjustFormAuditStatus.values()).map(e->{
                    SelectorDTO selectorDTO = new SelectorDTO();
                    selectorDTO.setName(e.getNameCh());
                    selectorDTO.setCode(e.name());
                    return selectorDTO;
                }).collect(Collectors.toList());
            case 2:
                return Arrays.stream(AdjustFormValidStatus.values()).map(e->{
                    SelectorDTO selectorDTO = new SelectorDTO();
                    selectorDTO.setName(e.getNameCh());
                    selectorDTO.setCode(e.name());
                    return selectorDTO;
                }).collect(Collectors.toList());
            default:
                return Lists.newArrayList();
        }
    }

    private void importDataVerify(List<PriceAdjustFormImportBaseBO> imports,List<PriceAdjustFormImportPublicDataBO> publicData){
        if (CollUtil.isEmpty(publicData)){
            throw new YxtBizException("名称和定/调价类别 sheet不能为空");
        }
        if (publicData.size()>1){
            throw new YxtBizException("名称和定/调价类别数据存在多条!");
        }
        if (StringUtils.isEmpty(publicData.get(0).getName())){
            throw new YxtBizException("调价单名称不能为空");
        }
        if (StringUtils.isEmpty(publicData.get(0).getPriceOpsType())){
            throw new YxtBizException("定/调价类别不能为空");
        }

        for (PriceAdjustFormImportBaseBO importBaseBO : imports){
            if (StringUtils.isEmpty(importBaseBO.getErpCode())){
                throw new YxtBizException("商品编码不能为空");
            }

            if (importBaseBO.getRetailPrice() == null && importBaseBO.getVipPrice() == null && importBaseBO.getChronicPrice() == null){
                throw new YxtBizException("需调整的价格不能全部为空!");
            }

            validateAdjustTime(importBaseBO.getRetailPrice(), importBaseBO.getRetailStartTime(), importBaseBO.getRetailEndTime());
            validateAdjustTime(importBaseBO.getVipPrice(), importBaseBO.getVipStartTime(), importBaseBO.getVipEndTime());
            validateAdjustTime(importBaseBO.getChronicPrice(), importBaseBO.getChronicStartTime(), importBaseBO.getChronicEndTime());
        }
    }


    private void validateAdjustTime(BigDecimal price, LocalDate startTime, LocalDate endTime) {
        if (price != null) {
            if (price.compareTo(BigDecimal.ZERO) <= 0){
                throw new YxtBizException("调整价格不能小于等于0");
            }
            if (price.scale() > 2) {
                throw new YxtBizException("价格最多两位小数");
            }

            if (startTime == null) {
                throw new YxtBizException("申请调整开始时间不能为空");
            }
            if (endTime == null) {
                throw new YxtBizException("申请调整结束时间不能为空");
            }
            if (startTime.isAfter(endTime)) {
                throw new YxtBizException("申请调整开始时间不能大于结束时间");
            }
        }
    }

    private PriceAdjustFormSaveReq importDataConvert(PriceAdjustFormImportBaseBO baseBO, List<PriceAdjustFormImportPublicDataBO> publicData,
                                                     PriceAdjustFormItemDTO item,String userId) {
        PriceAdjustFormSaveReq req = new PriceAdjustFormSaveReq();
        AdjustRoleType userRoleType = userService.getUserRoleType(userId);

        req.setName(publicData.get(0).getName());   //调价单名称
        req.setScope(userRoleType.getScope());      // 项目属性
        req.setAdjustType(AdjustType.getByNameCh(publicData.get(0).getPriceOpsType())); // 定调价类别
        req.setCreateUserRole(userRoleType);  // 报批人类别
        req.setStatus(AdjustFormAuditStatus.SUBMITTED);   //调价单状态 -- 提交(待审核)
        item.setErpCode(baseBO.getErpCode());    // 商品编码
        item.setRetailPrice(baseBO.getRetailPrice());    // 零售价
        item.setRetailPriceBeginTime(baseBO.getRetailStartTime());
        item.setRetailPriceEndTime(baseBO.getRetailEndTime());
        item.setVipPrice(baseBO.getVipPrice());  // 会员价
        item.setVipPriceBeginTime(baseBO.getVipStartTime());
        item.setVipPriceEndTime(baseBO.getVipEndTime());
        item.setChronicPrice(baseBO.getChronicPrice());  //  慢病价
        item.setChronicPriceBeginTime(baseBO.getChronicStartTime());
        item.setChronicPriceEndTime(baseBO.getChronicEndTime());
        req.setItems(Arrays.asList(item));
        return req;
    }




}
