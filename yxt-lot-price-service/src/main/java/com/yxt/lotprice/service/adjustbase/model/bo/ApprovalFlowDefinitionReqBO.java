package com.yxt.lotprice.service.adjustbase.model.bo;

import com.yxt.lang.dto.PageBase;
import com.yxt.lotprice.service.model.enums.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 调价审批流配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
public class ApprovalFlowDefinitionReqBO extends PageBase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 流程名称-模糊
     */
    private String name;

    /**
     * 项目属性-子公司/ 集团
     */
    private AdjustScopeType scope;

    /**
     * 1-启用；0-禁用
     */
    private AdjustStatusType status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;


    /**
     * 报批人角色
     */

    private String  submiterRoleCode;

    /**
     * 修改开始时间
     */
    private LocalDateTime startModifyTime;
    /**
     * 修改结束时间
     */
    private LocalDateTime endModifyTime;

    /**
     * 子公司编码
     */
    private List<String> companyCode;

    /**
     * 定价/调价
     */
    private List<AdjustType> adjustType;

    /**
     * 短流程/长流程
     */
    private List<AdjustFlowType> flowType;

    /**
     * 审批人角色
     */
    private List<AdjustRoleType> approvalRoleCodes;
}
