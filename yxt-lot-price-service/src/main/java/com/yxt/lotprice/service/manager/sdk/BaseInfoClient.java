/**
 * projectName: hydee-middle-data-sync
 * fileName: BaseInfoClient.java
 * packageName: cn.hydee.middle.data.sync.feign
 * date: 2022-06-10 17:08
 * copyright(c) 2022 http://www.hydee.cn/ Inc. All rights reserved.
 */
package com.yxt.lotprice.service.manager.sdk;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lotprice.common.model.constant.ServiceNameConstant;
import com.yxt.lotprice.service.manager.sdk.dto.QueryStoreDTO;
import com.yxt.lotprice.service.manager.sdk.dto.StoreDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 基础服务
 **/
@FeignClient(value = ServiceNameConstant.BASE_INFO)
public interface BaseInfoClient {

    /**
     * 查询所有门店-会返回门店所属区域编码
     * @param dto
     * @return
     */
    @PostMapping("/${api.base-info-version}/store/listAllV3")
    ResponseBase<List<StoreDTO>>listAllV2(@Valid @RequestBody QueryStoreDTO dto);
}