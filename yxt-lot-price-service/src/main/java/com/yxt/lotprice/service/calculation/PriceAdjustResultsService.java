package com.yxt.lotprice.service.calculation;

import com.yxt.lotprice.service.model.bo.PriceResultsBO;
import com.yxt.lotprice.service.model.bo.PriceResultsUpdateBO;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import com.yxt.lotprice.service.model.enums.log.PriceSourceEnum;

import java.util.Collection;
import java.util.List;

/**
 * 调价结果
 * Since: 2025/05/22 19:08
 * Author: qs
 */
public interface PriceAdjustResultsService {

    /**
     * 查询
     * @param tableName 不为空则直接使用计算出的表名称
     * @param companyCodes 公司编码
     * @param storeCodes 门店编码集合
     * @param erpCode 商品编码
     * @param priceType 价格类型
     * @return 价格数据
     */
    List<PriceResultsBO> listByErpCodePriceTypeStoreCode(String tableName, Collection<String> companyCodes, Collection<String> storeCodes, String erpCode, PriceTypeEnum priceType);

    /**
     * 查询单门店的调价结果
     * @param storeCode 门店编码集合
     * @param priceType 价格类型
     * @param erpCodes 商品编码
     * @return 价格数据
     */
    List<PriceResultsBO> listByStoreCodePriceTypeErpCodes(String storeCode, PriceTypeEnum priceType, Collection<String> erpCodes);

    // 批量保存调价结果，单商品情况
    void saveBatch(List<PriceResultsBO> resultsBOList, PriceSourceEnum sourceEnum);

    /**
     * 批量更新价格代和价格
     * 更新当前价格、下次价格、价格代
     */
    void updatePriceSegment(List<PriceResultsUpdateBO> resultsBOList, PriceSourceEnum sourceEnum);


    /**
     * 批量更新价格代，只更新价格代不更新价格
     * 更新当前价格、下次价格、价格代
     */
    void updatePriceSegmentOnly(List<PriceResultsUpdateBO> resultsBOList, PriceSourceEnum sourceEnum);

    /**
     * 批量更新价格
     * 更新当前价格、下次价格
     */
    void updatePrice(List<PriceResultsUpdateBO> resultsBOList, PriceSourceEnum sourceEnum);
}
