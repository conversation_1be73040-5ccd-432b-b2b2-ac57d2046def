package com.yxt.lotprice.service.model.bo;

import com.yxt.lotprice.service.model.enums.DimensionEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * 计算完成的价格段
 * Since: 2025/05/22 14:54
 * Author: qs
 */

@Data
@AllArgsConstructor
public class PriceTimelineSegmentBO {
    private LocalDate start;
    private LocalDate end;
    private BigDecimal price;
    private String formNo;
    private String formItemNo;
    private DimensionEnum orgType;
    private Set<String> allFormItemNos;
    List<PriceTimeSegment> mergeList;

    @Data
    public static class PriceTimeSegment {
        private LocalDate start;
        private LocalDate end;
        private BigDecimal price;
        private String formNo;
        private String formItemNo;
        private DimensionEnum orgType;
        private Set<String> allFormItemNos;
    }


}
