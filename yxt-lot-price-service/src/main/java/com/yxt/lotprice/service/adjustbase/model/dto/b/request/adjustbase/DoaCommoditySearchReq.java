package com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase;


import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Set;


@Data
@ApiModel("下放商品清单查询接口")
public class DoaCommoditySearchReq extends PageBase {

    @ApiModelProperty("erp编码")
    Set<String> erpCodes;

    @ApiModelProperty("商品名称")
    String commodityName;

    @ApiModelProperty("部门编码")
    Set<String> orgs;
}
