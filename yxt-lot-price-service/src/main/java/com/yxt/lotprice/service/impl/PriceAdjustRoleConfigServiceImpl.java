package com.yxt.lotprice.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lotprice.service.PriceAdjustRoleConfigService;
import com.yxt.lotprice.service.adjustbase.model.bo.PriceAdjustRoleConfigBO;
import com.yxt.lotprice.service.cache.AllUserInfoCache;
import com.yxt.lotprice.service.cache.OrgInfoCacheUtils;
import com.yxt.lotprice.service.manager.iface.PriceAdjustRoleConfigManager;
import com.yxt.lotprice.service.manager.iface.PriceAdjustRoleItemRelateManager;
import com.yxt.lotprice.service.manager.iface.PriceAdjustRoleRelateManager;
import com.yxt.lotprice.service.model.dto.b.request.*;
import com.yxt.lotprice.service.model.dto.b.response.*;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationDTO;
import com.yxt.lotprice.service.model.enums.*;
import com.yxt.org.read.opensdk.emp.dto.response.EmployeeScrollResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:18
 */
@Service
@Slf4j
public class PriceAdjustRoleConfigServiceImpl implements PriceAdjustRoleConfigService {

    @Resource
    private PriceAdjustRoleConfigManager priceAdjustRoleConfigManager;

    @Resource
    private PriceAdjustRoleRelateManager priceAdjustRoleRelateManager;

    @Resource
    private PriceAdjustRoleItemRelateManager priceAdjustRoleItemRelateManager;

    @Resource
    private AllUserInfoCache allUserInfoCache;
    @Override
    public PageDTO<PriceAdjustRangePageResp> priceAdjustRangePage(PriceAdjustRangePageReq req) {

        return priceAdjustRoleConfigManager.priceAdjustRangePage(req);
    }
    @Override
    public PageDTO<SubmitterPageResp> submitterPage(SubmitterPageReq req) {
        return priceAdjustRoleRelateManager.submitterPage(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addSubmitter(AddOrEditSubmitterReq req,String userName) {
        //新增报批人配置
        if (StrUtil.isEmpty(req.getCode())){
            String code = priceAdjustRoleRelateManager.addSubmitter(req, userName);
            req.setCode(code);
            priceAdjustRoleItemRelateManager.addSubmitterItem(req, userName);
        }else {
            //修改报批人配置
            priceAdjustRoleRelateManager.updateSubmitter(req, userName);
            priceAdjustRoleItemRelateManager.updateSubmitterItem(req, userName);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean addApprover(AddOrEditApproverReq req, String userName) {
        if (StrUtil.isEmpty(req.getCode())){
            //新增审批人配置
            String code = priceAdjustRoleRelateManager.addApprover(req, userName);
            req.setCode(code);
            priceAdjustRoleItemRelateManager.addApproverItem(req, userName);
        }else {
            //修改审批人配置
            priceAdjustRoleRelateManager.updateApprover(req, userName);
            priceAdjustRoleItemRelateManager.updateApproverItem(req, userName);
        }
        return Boolean.TRUE;
    }

    @Override
    public PageDTO<ApproverPageResp> approverPage(ApproverPageReq req) {
        PageDTO<ApproverPageResp> pageDTO = priceAdjustRoleRelateManager.approverPage(req);

        if (CollUtil.isNotEmpty(pageDTO.getData())) {
            List<ApproverPageResp> list = pageDTO.getData();
            list.forEach(item -> {
                SysOrganizationDTO sysOrganizationDTO = OrgInfoCacheUtils.getOrgListByUnitCode(item.getCompanyCode());
                if (sysOrganizationDTO != null) {
                    item.setCompanyName(sysOrganizationDTO.getOrName());
                }
            });
        }
        return pageDTO;
    }

    @Override
    public ApproverDetailResp approverDetail(String code) {
        //基础信息
        ApproverDetailResp approverDetailResp = priceAdjustRoleRelateManager.approverDetail(code);
        //审批人信息
        List<ItemRelateApproverResp> approverItemList = priceAdjustRoleItemRelateManager.getApproverItemList(code);
        approverDetailResp.setApproverList(approverItemList);

        return approverDetailResp;
    }

    @Override
    public SubmitterDetailResp submitterDetail(String code) {
        //基础信息
        SubmitterDetailResp submitterDetailResp = priceAdjustRoleRelateManager.submitterDetail(code);
        //组织信息
        List<ItemRelateSubmitterResp> submitterItemList = priceAdjustRoleItemRelateManager.getSubmitterItemList(code, submitterDetailResp.getScopeName());
        submitterDetailResp.setSubmitterList(submitterItemList);
        return submitterDetailResp;
    }

    @Override
    public List<SelectorDTO> roleSelector(RoleSelectorReq req) {
        List<AdjustRoleType> roleTypes = Arrays.stream(AdjustRoleType.values()).filter(adjustRoleType -> adjustRoleType.getScope().equals(req.getScopeType())
                && adjustRoleType.getRoleDimensionType().equals(req.getDimensionType())).collect(Collectors.toList());

        List<SelectorDTO> roleSelectorDTOS = new ArrayList<>();
        for (AdjustRoleType roleType : roleTypes) {
            SelectorDTO roleSelectorDTO = new SelectorDTO();
            roleSelectorDTO.setCode(roleType.name());
            roleSelectorDTO.setName(roleType.getNameCh());
            roleSelectorDTOS.add(roleSelectorDTO);
        }
        return roleSelectorDTOS;
    }

    @Override
    public List<SelectorDTO> priceTypeSelector() {
        List<SelectorDTO> priceTypeSelectorDTOS = new ArrayList<>();
        for (PriceTypeEnum value : PriceTypeEnum.values()) {
            SelectorDTO selectorDTO = new SelectorDTO();
            selectorDTO.setCode(value.getValue());
            selectorDTO.setName(value.getName());
            priceTypeSelectorDTOS.add(selectorDTO);
        }
        return priceTypeSelectorDTOS;
    }

    @Override
    public List<SelectorDTO> companySelector() {
        return priceAdjustRoleConfigManager.companySelector();
    }

    @Override
    public List<SelectorDTO> dimensionSelector() {
        List<SelectorDTO> dimensionSelectorDTOS = new ArrayList<>();
        for (DimensionEnum value : DimensionEnum.values()) {
            SelectorDTO selectorDTO = new SelectorDTO();
            selectorDTO.setCode(value.getValue());
            selectorDTO.setName(value.getNameCh());
            dimensionSelectorDTOS.add(selectorDTO);
        }
        return dimensionSelectorDTOS;
    }

    @Override
    public CheckSubmitterResp checkSubmitter(AddOrEditSubmitterReq req) {
        List<String> orgCode = req.getCompanyList().stream().map(ItemRelateCompanyReq::getRoleData).collect(Collectors.toList());
        if (CollUtil.isEmpty(orgCode)){
            throw new YxtBizException("请选择组织");
        }
        CheckSubmitterResp checkSubmitterResp = new CheckSubmitterResp();
        List<RepeatItemDTO> list = priceAdjustRoleItemRelateManager.checkSubmitterList(req.getRoleCode(), orgCode);
        if (CollUtil.isEmpty(list)){
            checkSubmitterResp.setIsRepeat(false);
        }else {
            checkSubmitterResp.setIsRepeat(true);
            checkSubmitterResp.setRepeatItemList(list);
        }
        return checkSubmitterResp;
    }

    @Override
    public LocateRoleConfigResp locateRoleConfig(LocateRoleConfigReq req) {
        LocateRoleConfigResp resp = new LocateRoleConfigResp();

        if (req.getCompanyCode() == null || StringUtils.isBlank(req.getCompanyCode())){
            resp.setCompanyCode(req.getCompanyCode());
            resp.setRole(req.getRole());
            resp.setTypeList(Arrays.stream(AdjustType.values()).map(x-> new SelectorDTO().setName(x.getNameCh()).setCode(x.name())).collect(Collectors.toList()));
            resp.setFlowTypeList(Arrays.stream(AdjustFlowType.values()).map(x-> new SelectorDTO().setName(x.getNameCh()).setCode(x.name())).collect(Collectors.toList()));
        }
        else {
            PriceAdjustRoleConfigBO condition = new PriceAdjustRoleConfigBO();
            condition.setRoleCode(req.getRole().name());
            condition.setCompanyCode(req.getCompanyCode());
            List<PriceAdjustRoleConfigBO> bos = priceAdjustRoleConfigManager.search(condition);
            if (CollectionUtils.isNotEmpty(bos)) {
                PriceAdjustRoleConfigBO priceAdjustRoleConfigBO = bos.get(0);
                resp.setCompanyCode(priceAdjustRoleConfigBO.getCompanyCode());
                resp.setRole(AdjustRoleType.valueOf(priceAdjustRoleConfigBO.getRoleCode()));
                resp.setTypeList(Arrays.stream(priceAdjustRoleConfigBO.getPriceOpsType().split(",")).filter(StringUtils::isNotBlank).map(AdjustType::valueOf).map(x -> new SelectorDTO().setName(x.getNameCh()).setCode(x.name())).collect(Collectors.toList()));
                resp.setFlowTypeList(Arrays.stream(priceAdjustRoleConfigBO.getFlowType().split(",")).filter(StringUtils::isNotBlank).map(AdjustFlowType::valueOf).map(x -> new SelectorDTO().setName(x.getNameCh()).setCode(x.name())).collect(Collectors.toList()));
            }
        }
        return resp;
    }

    @Override
    public PageDTO<ToEditPageResp> queryToEditPage(ToEditPageReq req) {
        //查询报批人设置的分部运营部
        PageDTO<ToEditPageResp> submitterOrgList = priceAdjustRoleItemRelateManager.getSubmitterOrgList(req);
        if (StrUtil.isNotEmpty(req.getCode())){
            //编辑页面去设置查询price_adjust_role_item_relate_ext
            List<String> orCodeList = submitterOrgList.getData().stream().map(ToEditPageResp::getOrCode).collect(Collectors.toList());

            List<RelateExtDTO> relateExtDtoList = priceAdjustRoleItemRelateManager.getItemRelateExtList(req.getCode(), AdjustRoleType.getByName(req.getRoleType()),orCodeList);
            Map<String, List<RelateExtDTO>> map = relateExtDtoList.stream().collect(Collectors.groupingBy(RelateExtDTO::getOrCode));

            for (ToEditPageResp datum : submitterOrgList.getData()) {
                List<RelateExtDTO> relateExtDTOList = map.get(datum.getOrCode());
                //获取用户信息
                List<UserDTO> userDTOList = new ArrayList<>();
                for (RelateExtDTO relateExtDTO : relateExtDTOList) {
                    UserDTO userDTO = new UserDTO();
                    userDTO.setUserId(relateExtDTO.getUserId());
                    userDTOList.add(userDTO);
                }
                datum.setUsers(userDTOList);
            }
        }
        return submitterOrgList;
    }

    @Override
    public PageDTO<UserDTO> userPage(UserPageReq req) {
        List<EmployeeScrollResDTO.EmployeeResDTO> allUserInfo = allUserInfoCache.getAllUserInfo();
        if (StrUtil.isNotEmpty(req.getUserCode()) && StrUtil.isNotEmpty(req.getUserName())){
            allUserInfo = allUserInfo.stream().filter(x -> x.getEmpCode().equals(req.getUserCode()) && x.getEmpName().equals(req.getUserName())).collect(Collectors.toList());
        }else if (StrUtil.isNotEmpty(req.getUserCode())){
            allUserInfo = allUserInfo.stream().filter(x -> x.getEmpCode().equals(req.getUserCode())).collect(Collectors.toList());
        }else if (StrUtil.isNotEmpty(req.getUserName())){
            allUserInfo = allUserInfo.stream().filter(x -> x.getEmpName().equals(req.getUserName())).collect(Collectors.toList());
        }
        PageDTO<UserDTO> pageDTO = new PageDTO<>(req.getCurrentPage(), req.getPageSize());

        if (CollUtil.isNotEmpty(allUserInfo)){
            allUserInfo = allUserInfo.stream().sorted(Comparator.comparing(EmployeeScrollResDTO.EmployeeResDTO::getEmpCode)).collect(Collectors.toList());
            int total = allUserInfo.size();
            int totalPages = (int) Math.ceil((double) total / req.getPageSize());
            // 检查页码是否有效
            if (req.getCurrentPage() < 1 || req.getCurrentPage() > totalPages) {
                return pageDTO;
            }
            pageDTO.setTotalCount((long) total);
            pageDTO.setTotalPage((long) totalPages);
            // 计算起始和结束索引
            int fromIndex = (int) ((req.getCurrentPage() - 1) * req.getPageSize());
            int toIndex = (int) Math.min(fromIndex + req.getPageSize(), total);
            allUserInfo = allUserInfo.subList(fromIndex, toIndex);

            List<UserDTO> userDTOList = new ArrayList<>();
            for (EmployeeScrollResDTO.EmployeeResDTO employeeResDTO : allUserInfo) {
                UserDTO userDTO = new UserDTO();
                userDTO.setUserId(employeeResDTO.getEmpCode());
                userDTO.setUserName(employeeResDTO.getEmpName());
                userDTOList.add(userDTO);
            }
            pageDTO.setData(userDTOList);
        }

        return pageDTO;
    }

    @Override
    public AdjustRoleType getCurrentUserRoleType(String orCode) {

        return priceAdjustRoleItemRelateManager.getCurrentUserRoleType(orCode);
    }
}
