package com.yxt.lotprice.service.model.dto.b.request;

import com.yxt.lang.dto.api.MiddleRequestBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class DemoBReq extends MiddleRequestBase {
    @ApiModelProperty("请求编号")
    private @NotBlank(
            message = "请求编号不能为空"
    ) String requestId;

    @ApiModelProperty("词典id")
    private @NotNull(
            message = "最大员工数"
    ) String maxStaffAmount;
}
