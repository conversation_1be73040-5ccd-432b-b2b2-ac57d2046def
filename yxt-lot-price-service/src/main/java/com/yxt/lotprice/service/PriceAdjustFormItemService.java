package com.yxt.lotprice.service;

import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemBO;
import com.yxt.lotprice.service.model.enums.DimensionEnum;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;

import java.util.List;

/**
 * Since: 2025/05/21 14:38
 * Author: qs
 */
public interface PriceAdjustFormItemService {

    PriceAdjustFormItemBO getById(Long id);

    /**
     * 根据类型获取调价单明细的门店范围
     * @param dimensionEnum 调价单机构类型
     * @param orgCode 机构编码
     * @return 门店编码
     */
    List<String> getStoreCodeByDimension(DimensionEnum dimensionEnum, String orgCode);

    /**
     * 调价单执行完成
     * @param id 调价单明细id
     * @return true/false
     */
    boolean formItemExecFinish(Long id);

    /**
     * 根据门店查询所有调价单明细
     * 分批查询
     * @return
     */
    List<PriceAdjustFormItemBO> listAllEnableByStoreCodeAndType(String storeCode, PriceTypeEnum priceTypeEnum);

    /**
     * 根据机构（门店编码、价格组编码、分公司编码）所有调价单明细
     */
    List<PriceAdjustFormItemBO> listAllEnableByOrgCodeAndType(String storeCode, PriceTypeEnum priceTypeEnum);
}
