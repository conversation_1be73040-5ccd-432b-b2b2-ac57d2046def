package com.yxt.lotprice.service;

import com.yxt.lotprice.service.adjustbase.model.bo.PriceAdjustRoleConfigBO;
import com.yxt.lotprice.service.manager.sdk.dto.StoreDTO;
import com.yxt.lotprice.service.model.dto.b.response.CurrentUserCompanyResp;
import com.yxt.lotprice.service.model.enums.AdjustRoleType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 18:37
 */
public interface UserService {

    AdjustRoleType getUserRoleType(String userId);

    PriceAdjustRoleConfigBO getUserRoleConfig(String userId,AdjustRoleType roleType);

    StoreDTO getUserStore(String userId);

    List<CurrentUserCompanyResp> currentUserCompany(String userId);
}
