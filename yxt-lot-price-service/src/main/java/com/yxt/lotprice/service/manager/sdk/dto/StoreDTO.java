package com.yxt.lotprice.service.manager.sdk.dto;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 员工查询返回DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/19 15:32
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class StoreDTO {

    @ApiModelProperty(value = "门店编码")
    private String stCode;

    @ApiModelProperty(value = "门店名称")
    private String stName;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "库存同步比例")
    private Double syncRatio;

    @ApiModelProperty(value = "机构类别 1-集团；2-分子公司；3-仓库；4-数据中心；5-部门；6-加盟店；7-直营店")
    private Integer stClass;

    @ApiModelProperty(value = "门店细分类型 70-直营店, 60-分支机构加盟, 61-独立法人加盟,90-便利店")
    private Integer subStClass;

    @ApiModelProperty(value = "是否上线门店（0非上线门店，1上线门店）")
    private Integer onlineStatus;

    @ApiModelProperty(value = "门店上级机构编码")
    private String orParentCode;

    @ApiModelProperty(value = "门店所属大仓")
    private String warehouseCode;

    @ApiModelProperty(value = "分公司编码 -- 传入了companyCodeList参数，才会返回这个值")
    private String companyCode;

    @ApiModelProperty(value = "所属分公司编码")
    private String branchCode;

    @ApiModelProperty(value = "门店状态")
    private Integer stStatus;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "区域代码")
    private String yxtStoreareaName;

    @ApiModelProperty(value = "分部代码")
    private String yxtStorebranchNumber;

    @ApiModelProperty(value = "门店所属配送组织-查门店批号效期库存")
    private String deliveryWarehouseCode;

    @ApiModelProperty(value = "价格组")
    private String pricegroupIdNumber;

    @ApiModelProperty(value = "价格组名称")
    private String pricegroupIdName;

    //判断当前门店是否是加盟店
    public boolean jmStore(){
        return subStClass  != null && subStClass == 61;
    }
}