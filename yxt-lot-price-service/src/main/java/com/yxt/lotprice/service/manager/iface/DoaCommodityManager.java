package com.yxt.lotprice.service.manager.iface;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.adjustbase.model.bo.PriceAdjustDoaCommodityBO;
import com.yxt.lotprice.service.adjustbase.model.bo.PriceAdjustDoaCommoditySearchBO;

import java.util.List;
import java.util.Set;

public interface DoaCommodityManager {
    List<PriceAdjustDoaCommodityBO> list(Set<String> erpCodes, Set<String> orgs);

    void batchSave(List<PriceAdjustDoaCommodityBO> data);

    PageDTO<PriceAdjustDoaCommodityBO> page(PriceAdjustDoaCommoditySearchBO bo);

    Long batchRemove(PriceAdjustDoaCommoditySearchBO bo);
}
