package com.yxt.lotprice.service;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.DoaCommodityRemoveReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.DoaCommoditySaveReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.DoaCommoditySearchReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase.DoaCommoditySearchResp;

public interface DoaCommodityService {
    String save(DoaCommoditySaveReq req, String userName);

    PageDTO<DoaCommoditySearchResp> page(DoaCommoditySearchReq req);

    Long remove(DoaCommodityRemoveReq req);
}
