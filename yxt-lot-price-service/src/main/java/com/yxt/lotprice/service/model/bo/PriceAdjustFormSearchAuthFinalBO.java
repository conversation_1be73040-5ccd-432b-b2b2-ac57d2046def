package com.yxt.lotprice.service.model.bo;

import com.yxt.lotprice.service.model.enums.AdjustRoleType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 调价申请单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
public class PriceAdjustFormSearchAuthFinalBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置的可以审核的部门
     * 针对部分非集团级，分子公司级的需要
     */
    private String orgCode;

    /**
     * 公司编码
     */
    private String companyCode;


    /**
     * 角色编码
     * 放入审批流快照中去查
     */
    private AdjustRoleType roleCode;


    /**
     * 机构id路径
     */
    private String parentOrgIdPath;


}
