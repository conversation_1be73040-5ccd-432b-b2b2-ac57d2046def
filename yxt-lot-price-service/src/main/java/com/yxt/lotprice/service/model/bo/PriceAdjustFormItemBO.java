package com.yxt.lotprice.service.model.bo;

import com.yxt.lotprice.service.model.enums.CancelStatusEnum;
import com.yxt.lotprice.service.model.enums.DimensionEnum;
import com.yxt.lotprice.service.model.enums.ItemExecStatusEnum;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Since: 2025/05/21 14:40
 * Author: qs
 */

@Data
public class PriceAdjustFormItemBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 调价单id
     */
    private Long parentId;

    /**
     * 调价单编码
     */
    private String parentCode;

    /**
     * 机构类型：分公司，价格组，门店
     */
    private DimensionEnum orgType;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 申请调/定价格
     */
    private BigDecimal requestPrice;

    /**
     * 申请调/定价生效开始时间
     */
    private LocalDateTime requestStartTime;

    /**
     * 申请调/定价生效结束时间
     */
    private LocalDateTime requestEndTime;

    /**
     * 作废状态
     */
    private CancelStatusEnum cancelStatus;

    /**
     * 审批通过调/定价格
     */
    private BigDecimal aporovalPrice;

    /**
     * 审批通过调/定价开始时间
     */
    private LocalDate aporovalStartTime;

    /**
     * 审批通过调/定价结束时间
     */
    private LocalDate aporovalEndTime;

    /**
     * 审批通过时间
     */
    private LocalDateTime aporovalTime;

    /**
     * 审批记录，json字符串，记录每一步操作步骤的前后的值，以及操作人
     */
    private String desc;

    /**
     * 创建人/调价单创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private LocalDateTime crateTime;

    /**
     * 修改人
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 调价单执行状态：未开始，进行中，已完成，失败
     */
    private ItemExecStatusEnum execStatus;

    /**
     * 集团/分公司
     */
    private String scope;

    /**
     * 申请调价类型：（会员价，零售价，慢病价）
     */
    private PriceTypeEnum requestPriceType;

    /**
     * 商品编码
     */
    private String erpCode;

}
