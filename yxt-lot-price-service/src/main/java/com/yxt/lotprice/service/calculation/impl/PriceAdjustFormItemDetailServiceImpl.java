package com.yxt.lotprice.service.calculation.impl;

import cn.hutool.core.collection.CollUtil;
import com.yxt.lotprice.common.util.MiddleIdClient;
import com.yxt.lotprice.service.cache.AllStoreInfoCache;
import com.yxt.lotprice.service.calculation.PriceAdjustFormItemDetailService;
import com.yxt.lotprice.service.manager.iface.PriceAdjustFormItemDetailManager;
import com.yxt.lotprice.service.manager.sdk.dto.StoreDTO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemBO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemDetailBO;
import com.yxt.lotprice.service.model.enums.CancelStatusEnum;
import com.yxt.lotprice.service.model.enums.DimensionEnum;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Since: 2025/05/21 18:10
 * Author: qs
 */

@Service
public class PriceAdjustFormItemDetailServiceImpl implements PriceAdjustFormItemDetailService {

    @Resource
    private PriceAdjustFormItemDetailManager priceAdjustFormItemDetailManager;

    @Resource
    private AllStoreInfoCache allStoreInfoCache;

    @Override
    public boolean listByStoreAndPriceGroup(String storeCode) {
        return false;
    }

    @Override
    public boolean saveFormItemDetail(PriceAdjustFormItemBO formItemBO) {
        if (formItemBO == null) {
            return false;
        }

        List<PriceAdjustFormItemDetailBO> detailPOList = new ArrayList<>();

        // 根据机构类型处理不同逻辑
        if (DimensionEnum.STORE.equals(formItemBO.getOrgType())) {
            // 门店维度直接添加一条数据
            PriceAdjustFormItemDetailBO detailPO = buildDetailPO(formItemBO, formItemBO.getOrgCode());
            detailPOList.add(detailPO);
        } else if (DimensionEnum.PRICE_GROUP.equals(formItemBO.getOrgType())) {
            // 价格组维度，查询该价格组下所有门店
            List<StoreDTO> stores = allStoreInfoCache.getAllStoreByPriceGroup(formItemBO.getOrgCode());
            if (CollUtil.isNotEmpty(stores)) {
                for (StoreDTO store : stores) {
                    PriceAdjustFormItemDetailBO detailPO = buildDetailPO(formItemBO, store.getStCode());
                    detailPOList.add(detailPO);
                }
            }
        } else if (DimensionEnum.COMPANY.equals(formItemBO.getOrgType())) {
            // 分公司维度，查询该分公司下所有门店
            List<StoreDTO> stores = allStoreInfoCache.getAllStoreByCompanyCode(formItemBO.getOrgCode());
            if (CollUtil.isNotEmpty(stores)) {
                for (StoreDTO store : stores) {
                    PriceAdjustFormItemDetailBO detailPO = buildDetailPO(formItemBO, store.getStCode());
                    detailPOList.add(detailPO);
                }
            }
        }

        // 批量插入数据
        if (CollUtil.isNotEmpty(detailPOList)) {
            ListUtils.partition(detailPOList, 1000).forEach(list -> {
                // 正常场景都为插入，补偿场景下会有更新。优先批量插入，
                priceAdjustFormItemDetailManager.insertBatch(list);
            });
        }

        return true;
    }

    @Override
    public boolean deleteByStoreAndPriceGroup(String storeCode) {
        return false;
    }

    @Override
    public List<PriceAdjustFormItemDetailBO> listEnableByStoreCodeErpCodes(PriceTypeEnum priceTypeEnum, String storeCode, Collection<String> erpCodes) {
        return priceAdjustFormItemDetailManager.listEnableByStoreCodeErpCodes(priceTypeEnum, storeCode, erpCodes);
    }

    /**
     * 构建明细PO对象
     *
     * @param formItemBO 调价单项目BO
     * @param storeCode 门店编码
     * @return 调价单明细PO
     */
    private PriceAdjustFormItemDetailBO buildDetailPO(PriceAdjustFormItemBO formItemBO, String storeCode) {
        StoreDTO store = allStoreInfoCache.getStore(storeCode);
        if (store == null) {
            return null;
        }

        PriceAdjustFormItemDetailBO detailPO = new PriceAdjustFormItemDetailBO();
        // 设置ID，使用中间件生成ID
        // 设置调价单编码
        detailPO.setFormNo(formItemBO.getParentCode());
        // 设置调价单明细编码
        detailPO.setFormItemNo(String.valueOf(formItemBO.getId()));
        // 设置机构类型
        detailPO.setOrgType(formItemBO.getOrgType());
        // 设置ERP商品编码
        detailPO.setErpCode(formItemBO.getErpCode());
        // 设置门店ID
        detailPO.setStoreId(store.getId());
        // 设置门店编码
        detailPO.setStoreCode(storeCode);
        // 设置分公司编码
        detailPO.setCompanyCode(store.getCompanyCode());
        // 设置价格类型
        detailPO.setPriceType(formItemBO.getRequestPriceType());
        // 设置价格，优先使用审批价格，如果没有则使用申请价格
        detailPO.setPrice(formItemBO.getAporovalPrice());
        // 设置生效开始时间
        detailPO.setStartTime(formItemBO.getAporovalStartTime());
        // 设置生效结束时间
        detailPO.setEndTime(formItemBO.getAporovalEndTime());
        // 设置审批通过时间
        detailPO.setAporovalTime(formItemBO.getAporovalTime());
        // 设置启用状态为未作废
        detailPO.setEnableStatus(formItemBO.getCancelStatus());

        return detailPO;
    }
}
