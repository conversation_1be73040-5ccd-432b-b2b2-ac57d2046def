package com.yxt.lotprice.service.model.dto.b.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/20 15:10
 */
@Data
@ApiModel("审批人配置详情结果")
public class ApproverDetailResp {
    @ApiModelProperty("配置编码")
    private String code;
    @ApiModelProperty("配置名称")
    private String name;
    @ApiModelProperty("项目属性：GROUP-集团，COMPANY-子公司")
    private String scopeName;
    @ApiModelProperty("子公司名称")
    private String companyName;
    @ApiModelProperty("子公司编码")
    private String companyCode;
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("审批人配置")
    private List<ItemRelateApproverResp> approverList;
}
