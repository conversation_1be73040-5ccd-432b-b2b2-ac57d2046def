package com.yxt.lotprice.service.manager.iface;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.adjustbase.model.bo.PriceAdjustRoleConfigBO;
import com.yxt.lotprice.service.model.dto.b.request.LocateRoleConfigReq;
import com.yxt.lotprice.service.model.dto.b.request.PriceAdjustRangePageReq;
import com.yxt.lotprice.service.model.dto.b.response.PriceAdjustRangePageResp;
import com.yxt.lotprice.service.model.dto.b.response.SelectorDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:18
 */
public interface PriceAdjustRoleConfigManager {

    PageDTO<PriceAdjustRangePageResp> priceAdjustRangePage(PriceAdjustRangePageReq req);

    List<SelectorDTO> companySelector();

    List<PriceAdjustRoleConfigBO> search(PriceAdjustRoleConfigBO req);
}
