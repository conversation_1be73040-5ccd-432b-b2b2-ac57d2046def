package com.yxt.lotprice.service.model.enums;

import lombok.Getter;

@Getter
public enum AdjustScopeType {
    COMPANY("分公司"),
    GROUP("集团");

    AdjustScopeType(String name) {
        this.nameCh = name;
    }
    private String nameCh;

    public static AdjustScopeType getByName(String name) {
        try {
            return AdjustScopeType.valueOf(name);
        }catch (Exception e){
        }
        return null;
    }
}
