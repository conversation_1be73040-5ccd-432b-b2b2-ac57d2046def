package com.yxt.lotprice.service.model.dto.b.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/5/21 17:17
 */
@Data
@ApiModel(value = "下拉框")
@Accessors(chain = true)
public class SelectorDTO {
    @ApiModelProperty(value = "类别编码")
    private String code;
    @ApiModelProperty(value = "类别名称")
    private String name;
}
