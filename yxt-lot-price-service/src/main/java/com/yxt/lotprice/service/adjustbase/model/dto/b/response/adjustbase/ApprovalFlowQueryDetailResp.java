package com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase;


import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.ApprovalFlowSaveDetailReq;
import com.yxt.lotprice.service.model.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("审批流详情返回对象")
public class ApprovalFlowQueryDetailResp{

    @ApiModelProperty("审批流项目属性-集团/子公司    COMPANY(\"分公司\"),\n" +
            "    GROUP(\"集团\");")
    private AdjustScopeType scope;

    @ApiModelProperty("提交人角色编码")
    private AdjustRoleType submiterRoleCode;

    @ApiModelProperty("所属公司编码")
    private String companyCode;

    @ApiModelProperty("维度    ADJUSTMENT(\"定价\"),\n" +
            "    PRICING(\"新品调价\"),\n" +
            "    GIFT_PRICING(\"赠品定价/调价\");")
    private AdjustType adjustType;

    @ApiModelProperty("审批流类型    LONG(\"长流程\"),\n" +
            "    SHORT(\"短流程\"),\n" +
            "    NONE(\"不区分长短流程\");")
    private AdjustFlowType flowType;

    @ApiModelProperty("审批流类型    ON,\n" +
            "    OFF;")
    private AdjustStatusType status;

    @ApiModelProperty("审批流名称")
    private String name;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("code")
    private String code;

    @ApiModelProperty("审批流节点列表")
    private List<ApprovalFlowSaveDetailReq.ApprovalFlowNodeItem> configNodeList;

    @Data
    @ApiModel("审批流节点配置信息")
    public static class ApprovalFlowNodeItem{
        @ApiModelProperty("序号")
        private String no;

        @ApiModelProperty("id")

        private Long id;

        @ApiModelProperty("id")
        private String name;

        @ApiModelProperty("审批人职能类别")
        private AdjustRoleType roleCode;

        @ApiModelProperty("备注")
        private String desc;
    }
}
