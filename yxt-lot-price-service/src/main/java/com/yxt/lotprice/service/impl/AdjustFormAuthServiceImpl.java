package com.yxt.lotprice.service.impl;

import com.google.common.collect.Lists;
import com.yxt.lotprice.service.AdjustFormAuthService;
import com.yxt.lotprice.service.PriceAdjustRoleConfigService;
import com.yxt.lotprice.service.adjustbase.model.bo.ApprovalFlowDefinitionReqBO;
import com.yxt.lotprice.service.cache.AllUserInfoCache;
import com.yxt.lotprice.service.cache.OrgInfoCacheUtils;
import com.yxt.lotprice.service.manager.iface.AdjustFormAuthManager;
import com.yxt.lotprice.service.manager.iface.ApprovalFlowManager;
import com.yxt.lotprice.service.manager.iface.ApprovalFlowTaskManager;
import com.yxt.lotprice.service.manager.iface.PriceAdjustRoleItemRelateManager;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormSearchAuthFinalBO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormSearchAuthOriginBO;
import com.yxt.lotprice.service.model.bo.PriceAdjustRoleItemRelateBO;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationDTO;
import com.yxt.lotprice.service.model.enums.AdjustRoleType;
import com.yxt.org.read.opensdk.emp.dto.response.EmployeeScrollResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdjustFormAuthServiceImpl implements AdjustFormAuthService {


    @Autowired
    private PriceAdjustRoleItemRelateManager roleItemRelateManager;

    @Autowired
    private AllUserInfoCache allUserInfoCache;

    @Override
    public List<PriceAdjustRoleItemRelateBO> fillOriginAuthData(Integer tabType, String userId){
        List<PriceAdjustRoleItemRelateBO> list = new ArrayList<>();
        //todo,员工所属机构的机构路径
        if (tabType == null){
            log.error("tabType为空！");
            //todo 打印日志
        }else if(tabType == 1){//我发起的
            PriceAdjustRoleItemRelateBO authOriginBO = new PriceAdjustRoleItemRelateBO();
            authOriginBO.setUserId(userId);
            list.add(authOriginBO);
        }else if (tabType == 2){//待我处理的

        }else if (tabType == 3){//我关注的（我有权限查看的）
            EmployeeScrollResDTO.EmployeeResDTO userInfo = allUserInfoCache.getUserInfo(userId);
            PriceAdjustRoleItemRelateBO authOriginBO = new PriceAdjustRoleItemRelateBO();
            authOriginBO.setUserId(userId);
            authOriginBO.setOrgCode(userInfo.getSubOrgCode());
            authOriginBO.setCompanyCode(userInfo.getSubCompCode());
            list.add(authOriginBO);
        }else {
            log.error("tabType值不正确，只能是1,2,3！");
        }
        return list;
    }

    @Override
    public List<PriceAdjustFormSearchAuthFinalBO> fillIApproveAuthData(String userId){

        //1.根据审批人用户id，找到审批人角色
        List<PriceAdjustRoleItemRelateBO>  roleItemRelates =  roleItemRelateManager.getRoleItemRelateByAppoverUserId(userId);
        List<String> companys = roleItemRelates.stream().map(PriceAdjustRoleItemRelateBO::getCompanyCode).distinct().collect(Collectors.toList());
        if (companys.size() > 1){
            log.error("用户{}的审批人角色分布在多个分公司,查调价单时，只查询第一个分公司/集团的。详情{}！",userId,roleItemRelates);
            roleItemRelates = roleItemRelates.stream().filter(roleItemRelate -> roleItemRelate.getCompanyCode().equals(companys.get(0))).collect(Collectors.toList());
        }
        return roleItemRelates.stream().map(roleItemRelate -> {
            PriceAdjustFormSearchAuthFinalBO finalBO = new PriceAdjustFormSearchAuthFinalBO();
            finalBO.setOrgCode(roleItemRelate.getOrgCode());
            finalBO.setCompanyCode(roleItemRelate.getCompanyCode());
            finalBO.setRoleCode(roleItemRelate.getApproverRoleCode());
            return finalBO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<PriceAdjustFormSearchAuthFinalBO> fillIFoucsAuthData(String userId) {
        EmployeeScrollResDTO.EmployeeResDTO userInfo = allUserInfoCache.getUserInfo(userId);
        SysOrganizationDTO org = OrgInfoCacheUtils.getOrgByCode(userInfo.getSubOrgCode());
        if  (org != null){
            PriceAdjustFormSearchAuthFinalBO authBO = new PriceAdjustFormSearchAuthFinalBO();
            authBO.setParentOrgIdPath(org.getParentPath());
            return Lists.newArrayList(authBO);
        }
        return null;
    }


}
