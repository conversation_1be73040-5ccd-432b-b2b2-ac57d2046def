package com.yxt.lotprice.service.manager.iface;

import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemDetailBO;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import java.util.Collection;
import java.util.List;

/**
 * Since: 2025/05/22 9:51
 * Author: qs
 */
public interface PriceAdjustFormItemDetailManager {

    /**
     * 查询门店商品调价单价格明细
     * @param storeCode 门店编码
     * @param erpCodes 商品编码
     * @return 明细集合
     */
    List<PriceAdjustFormItemDetailBO> listEnableByStoreCodeErpCodes(PriceTypeEnum priceTypeEnum, String storeCode, Collection<String> erpCodes);

    /**
     * 批量插入
     * @param records
     * @return
     */
    boolean insertBatch(Collection<PriceAdjustFormItemDetailBO> records);

}
