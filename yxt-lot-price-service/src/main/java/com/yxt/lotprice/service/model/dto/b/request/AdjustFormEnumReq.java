package com.yxt.lotprice.service.model.dto.b.request;

import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/5/29 10:08
 */
@Data
@ApiModel("调价单相关枚举值查询")
public class AdjustFormEnumReq {
    @ApiModelProperty("枚举值查询：1、调价单状态；2.作废状态")
    @NotNull(message = "枚举值不能为空：1、调价单状态；2.作废状态")
    private Integer type;
}
