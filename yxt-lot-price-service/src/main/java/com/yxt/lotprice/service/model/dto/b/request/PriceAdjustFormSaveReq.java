package com.yxt.lotprice.service.model.dto.b.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.lotprice.service.adjustbase.model.bo.ApprovalFlowDefinitionBO;
import com.yxt.lotprice.service.model.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/26 15:11
 */
@Data
@ApiModel("调价单保存参数")
public class PriceAdjustFormSaveReq {
    @ApiModelProperty("调价单id")
    @JsonIgnore
    private Long id;
    @ApiModelProperty("调价单编号")
    private String code;
    @ApiModelProperty("调价单名称")
    private String name;
    @ApiModelProperty("项目属性")
    private AdjustScopeType scope;
    @ApiModelProperty("定价/调价维度 STORE-门店,PRICE_GROUP-价格组,COMPANY-子公司")
    private DimensionEnum dimension;
    @ApiModelProperty("定价/调价类别")
    private AdjustType adjustType;
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("报批人类别")
    private AdjustRoleType createUserRole;

    @ApiModelProperty("DRAFT:暂存 SUBMITTED:提交")
    private AdjustFormAuditStatus status;

    @ApiModelProperty("商品信息")
    private List<PriceAdjustFormItemDTO> items;

    @ApiModelProperty("审批流定义")
    @JsonIgnore
    private ApprovalFlowDefinitionBO approvalFlowDefinition;

    @ApiModelProperty("审批流长短流程")
    @JsonIgnore
    private AdjustFlowType flowType;
}
