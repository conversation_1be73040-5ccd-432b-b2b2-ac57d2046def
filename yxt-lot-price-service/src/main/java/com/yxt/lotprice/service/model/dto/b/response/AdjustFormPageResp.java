package com.yxt.lotprice.service.model.dto.b.response;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.lotprice.service.model.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("调价单分页查询返回对象")
public class AdjustFormPageResp {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("调价单状态    DRAFT(\"草稿\", \"DRAFT\", 1),\n" +
            "\n" +
            "    SUBMITTED(\"待审核\", \"SUBMITTED\", 2),\n" +
            "    PROCESSING(\"审核中\", \"PROCESSING\", 3),\n" +
            "\n" +
            "    APPROVED(\"已审核\", \"APPROVED\", 99),\n" +
            "    REJECTED(\"已驳回\", \"REJECTED\", 99),\n" +
            "    CANCELED(\"已撤销\", \"CANCELED\", 99);")
    private AdjustFormAuditStatus status;
    @ApiModelProperty("调价单状态名称")
    private  String statusName;

    @ApiModelProperty("审批流项目属性-集团/子公司    COMPANY(\"分公司\"),\n" +
            "    GROUP(\"集团\");")
    private AdjustScopeType scope;
    @ApiModelProperty("审批流项目属性-集团/子公司")
    private String scopeName;

    @ApiModelProperty("定/调价维度    STORE(\"STORE\", \"门店\", 1),\n" +
            "    PRICE_GROUP(\"PRICE_GROUP\", \"价格组\", 2),\n" +
            "    COMPANY(\"COMPANY\", \"子公司\", 3),")
    private DimensionEnum dimension;
    @ApiModelProperty("定/调价维度名称")
    private String dimensionName;

    @ApiModelProperty("审核通过时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approvalTime;

    @ApiModelProperty("修改人")
    private String modifyName;


    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyTime;

    @ApiModelProperty("创建人")
    private String createName;

    @ApiModelProperty("创建人角色编码")
    private AdjustRoleType createUserRole;

    @ApiModelProperty("创建人角色名称")
    private String createRoleName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("复制标识，为1展示复制按钮")
    private Integer copyFlag;

    @ApiModelProperty("作废标识，为1展示整单作废/明细作废按钮")
    private Integer cancelFlag;

    @ApiModelProperty("编辑标识，为1展示编辑按钮")
    private Integer editFlag;

    @ApiModelProperty("驳回标识，为1展示驳回按钮")
    private Integer rejectFlag;

    @ApiModelProperty("撤销表示，为1表示可以撤销")
    private Integer invalidFlag;

    @ApiModelProperty("去审核，为1表示可以去审核")
    private Integer auditFlag;
}
