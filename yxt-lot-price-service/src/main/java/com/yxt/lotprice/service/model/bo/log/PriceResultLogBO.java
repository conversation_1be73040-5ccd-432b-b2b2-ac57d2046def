package com.yxt.lotprice.service.model.bo.log;

import com.yxt.lotprice.service.model.bo.PriceTimelineSegmentBO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Since: 2025/05/27 11:23
 * Author: qs
 */
@Data
public class PriceResultLogBO {

    /**
     * 价格计算来源
     */
    private String source;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 价格代
     */
    private List<PriceTimelineSegmentBO> priceResultSegment;
}
