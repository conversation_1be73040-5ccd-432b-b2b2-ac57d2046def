package com.yxt.lotprice.service.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/19 15:13
 */
@Getter
@AllArgsConstructor
public enum DimensionEnum {
    STORE("STORE", "门店", 1),
    PRICE_GROUP("PRICE_GROUP", "价格组", 2),
    COMPANY("COMPANY", "子公司", 3),
    ;

    private String value;
    private String nameCh;
    // 优先级
    private final int priority;

    public static DimensionEnum getByValue(String value) {
        for (DimensionEnum item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}
