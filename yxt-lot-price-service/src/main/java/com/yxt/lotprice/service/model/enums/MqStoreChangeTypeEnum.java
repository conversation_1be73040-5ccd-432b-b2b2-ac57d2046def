package com.yxt.lotprice.service.model.enums;

import lombok.Getter;

/**
 * Since: 2025/05/28 15:05
 * Author: qs
 */
public enum MqStoreChangeTypeEnum {

    STORE_ADD("store_add", "门店新增"),
    STORE_MODIFY("store_modify", "门店修改"),
    PRICE_GROUP_MODIFY("price_group_modify", "价格组修改"),
    BRANCH_CODE_MODIFY("branch_code_modify", "分公司修改"),
    BRANCH_PART_CODE_MODIFY("branch_part_code_modify", "分部编码修改"),
    AREA_CODE_MODIFY("area_code_modify", "区域修改"),
    BUSINESS_SCOPE_CODE_MODIFY("business_scope_code_modify", "经营范围修改"),
    ST_CLASS_MODIFY("st_class_modify", "门店类型修改");

    @Getter
    private final String value;
    @Getter
    private final String desc;

    MqStoreChangeTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据 value 获取枚举
     */
    public static MqStoreChangeTypeEnum fromValue(String value) {
        for (MqStoreChangeTypeEnum type : MqStoreChangeTypeEnum.values()) {
            if (type.getValue().equalsIgnoreCase(value)) {
                return type;
            }
        }
        return null;
    }
}
