package com.yxt.lotprice.service.cache;

import cn.hutool.core.collection.CollUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.org.read.opensdk.emp.dto.request.EmployeeScrollQueryReqDTO;
import com.yxt.org.read.opensdk.emp.dto.response.EmployeeScrollResDTO;
import com.yxt.org.read.opensdk.emp.service.EmpQueryOpenApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/26 13:49
 */
@Component
@Slf4j
public class AllUserInfoCache implements InitializingBean, DisposableBean {
    // 所有用户key
    private static final String ALL_USER_KEY = "ALL_USER";

    private static final int BATCH_SIZE = 2000;

    @Resource
    private EmpQueryOpenApi empQueryOpenApi;

    @Value("${spring.profiles.active:pro}")
    private String profiles;

    @Value("#{'${usercache.search.orglist:1006,yxt,6004}'.split(',')}")
    private List<String> orgCodeList;


    private LoadingCache<String, Map<String, EmployeeScrollResDTO.EmployeeResDTO>> allUserInfoCache;

    @Override
    public void destroy() throws Exception {
        // 关闭缓存
        if (Objects.nonNull(allUserInfoCache)) {
            allUserInfoCache.cleanUp();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        allUserInfoCache = Caffeine.newBuilder()
                // 设置过期时间
                .expireAfterWrite(60, TimeUnit.MINUTES).refreshAfterWrite(55, TimeUnit.MINUTES)
                .initialCapacity(4)
                // 缓存如果没有，loadingcache会自动加zai
                // 缓存key的最大个数
                .maximumSize(16)
                .build(key -> loadCache());
        preLoadCache();
    }

    public void preLoadCache() {
        log.info("用户缓存预加载");
        allUserInfoCache.get(ALL_USER_KEY);
    }

    private Map<String, EmployeeScrollResDTO.EmployeeResDTO> loadCache() {
        log.info("用户缓存加载");
        long begin = System.currentTimeMillis();
        String nextScrollId = null;
        List<EmployeeScrollResDTO.EmployeeResDTO> employeeResDTOList = new ArrayList<>();
        List<EmployeeScrollResDTO.EmployeeResDTO> currentEmployeeList = Collections.emptyList();
        do {
            try {
                EmployeeScrollQueryReqDTO reqDTO = new EmployeeScrollQueryReqDTO();
                reqDTO.setMerCode("500001");
                reqDTO.setScrollId(nextScrollId);
                reqDTO.setScrollSize(2000);
                reqDTO.setEmpType(0);
                if ("local".equals(profiles) && CollectionUtils.isNotEmpty(orgCodeList)){
                    reqDTO.setSubCompCodeList(orgCodeList);
                }
                ResponseBase<EmployeeScrollResDTO> responseBase = empQueryOpenApi.scrollQueryEmployeeAndExtendInfoById(reqDTO);
                if (responseBase.checkSuccess()) {
                    EmployeeScrollResDTO data = responseBase.getData();
                    nextScrollId = data.getNextScrollId();

                    currentEmployeeList = data.getData();
                    if (CollUtil.isEmpty(currentEmployeeList)){
                        break;
                    }else {
                        employeeResDTOList.addAll(currentEmployeeList);
                    }
                }
            }catch (Exception e){
                log.warn("用户缓存加载异常",e);
                break;
            }
        }while (CollUtil.isNotEmpty(currentEmployeeList) && currentEmployeeList.size() == BATCH_SIZE);

        log.info("用户缓存加载完成，耗时：{}ms",System.currentTimeMillis()-begin);
        if(CollUtil.isNotEmpty(employeeResDTOList)){
            return employeeResDTOList.stream().collect(Collectors.toMap(EmployeeScrollResDTO.EmployeeResDTO::getId, e->e));
        }
        return new HashMap<>();
    }

    public List<EmployeeScrollResDTO.EmployeeResDTO> getAllUserInfo() {
        return new ArrayList<>(Objects.requireNonNull(allUserInfoCache.get(ALL_USER_KEY)).values());
    }


    public EmployeeScrollResDTO.EmployeeResDTO getUserInfo(String userId){
        Map<String, EmployeeScrollResDTO.EmployeeResDTO> userInfoMap = allUserInfoCache.get(ALL_USER_KEY);
        return userInfoMap.getOrDefault(userId, new EmployeeScrollResDTO.EmployeeResDTO());
    }
}
