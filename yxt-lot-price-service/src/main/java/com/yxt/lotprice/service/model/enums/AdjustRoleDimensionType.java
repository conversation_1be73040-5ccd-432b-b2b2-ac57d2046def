package com.yxt.lotprice.service.model.enums;

import lombok.Getter;

@Getter
public enum AdjustRoleDimensionType {
    APPROVER("审批人"),
    SUBMITTER("报批人");


    AdjustRoleDimensionType(String name) {
        this.nameCh = name;
    }
    private String nameCh;

    public static AdjustRoleDimensionType getByName(String name) {
        try {
            return AdjustRoleDimensionType.valueOf(name);
        }catch (Exception e){
        }
        return null;
    }
}
