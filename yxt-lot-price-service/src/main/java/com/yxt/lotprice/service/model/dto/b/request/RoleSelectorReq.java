package com.yxt.lotprice.service.model.dto.b.request;

import com.yxt.lotprice.service.model.enums.AdjustRoleDimensionType;
import com.yxt.lotprice.service.model.enums.AdjustScopeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/21 17:23
 */
@Data
@ApiModel(value = "角色选择器请求参数")
public class RoleSelectorReq {
    @ApiModelProperty(value = "项目属性")
    private AdjustScopeType scopeType;
    @ApiModelProperty(value = "审批人/报批人")
    private AdjustRoleDimensionType dimensionType;
}
