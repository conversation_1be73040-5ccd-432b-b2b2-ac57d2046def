package com.yxt.lotprice.service.model.dto.b.response;

import com.yxt.lotprice.service.manager.sdk.dto.StoreDTO;
import com.yxt.lotprice.service.model.enums.AdjustScopeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28 13:50
 */
@Data
@ApiModel("当前用户调价范围权限")
public class CurrentUserAdjustScopeResp {
    @ApiModelProperty("项目属性")
    private AdjustScopeType scope;
    @ApiModelProperty("定价/调价维度 STORE-门店,PRICE_GROUP-价格组,COMPANY-子公司")
    private List<SelectorDTO> dimensionList;
    @ApiModelProperty("定价/调价类别")
    private List<SelectorDTO> adjustTypeList;
    @ApiModelProperty("价格类型")
    private List<SelectorDTO> priceTypeList;

    @ApiModelProperty("门店信息")
    private StoreDTO store;
}
