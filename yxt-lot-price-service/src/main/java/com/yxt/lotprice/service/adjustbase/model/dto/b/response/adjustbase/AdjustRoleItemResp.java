package com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase;


import com.yxt.lotprice.service.model.enums.AdjustRoleDimensionType;
import com.yxt.lotprice.service.model.enums.AdjustRoleType;
import com.yxt.lotprice.service.model.enums.AdjustScopeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("调价审批流角色查询返回值")
public class AdjustRoleItemResp {
    @ApiModelProperty("角色名称(中文)")
    private String roleNameCh;

    @ApiModelProperty("角色编码")
    private AdjustRoleType role;

    @ApiModelProperty("角色属性    COMPANY(\"分公司\"),\n" +
            "    GROUP(\"集团\");")
    private AdjustScopeType scope;

    @ApiModelProperty("角色类型    APPROVER(\"审批人\"),\n" +
            "    SUBMITTER(\"报批人\");\n")
    private AdjustRoleDimensionType roleDimensionType;

    @ApiModelProperty("角色优先级，值越大，优先级越高")
    private Integer order;

}
