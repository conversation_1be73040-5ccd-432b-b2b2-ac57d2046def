package com.yxt.lotprice.service.model.dto.b.response;

import com.yxt.lotprice.service.model.enums.AdjustRoleType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/19 18:19
 */
@Data
@ApiModel("报批人组织配置")
public class ItemRelateSubmitterResp {
    @ApiModelProperty("分公司编码")
    private String companyCode;

    @ApiModelProperty("所属子公司名称")
    private String companyName;

    @ApiModelProperty("分部编码")
    private String branchCode;

    @ApiModelProperty("分部名称")
    private String branchName;

    @ApiModelProperty("组织编码")
    private String roleData;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("报批人类型")
    private AdjustRoleType roleType;
}
