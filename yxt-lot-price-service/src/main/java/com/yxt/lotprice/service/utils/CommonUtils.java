package com.yxt.lotprice.service.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 常用公共方法类
 * @Date 2021/3/1 16:05
 */
public class CommonUtils {

    /**
     * 获取文件后缀
     *
     * @param fileName 文件名称
     * @param hasDot   是否带点  true带
     * @return
     */
    public static String getSuffix(String fileName, Boolean hasDot) {
        if (Objects.equals(Boolean.TRUE, hasDot)) {
            return fileName.substring(fileName.lastIndexOf("."));
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }


    /**
     * 拼接字符串以;拼接
     *
     * @param oldString 旧字符串
     * @param newString 新字符串
     * @return 拼接后结果
     */
    public static String concatenateString(String oldString, String newString) {
        if (StringUtils.isEmpty(oldString)) {
            return newString;
        } else {
            return oldString + ";" + newString;
        }
    }

    /**
     * 生成业务单号
     *
     * @return
     */
    public static String getOrderNo() {
        return DateUtil.format(new Date(), "yyyyMMddHHmmss") + RandomUtil.randomNumbers(8);
    }

    public static String getFileName(String filePrefix, String suffix) {
        return filePrefix + DateUtil.format(new Date(), "yyyyMMddHHmmss") + RandomUtil.randomNumbers(8) + suffix;
    }


    /**
     * 如果source为空则返回defaultV. -- Integer
     *
     * @param source
     * @param defaultV
     * @return
     */
    public static Integer nullToDefault(Integer source, Integer defaultV) {
        return Objects.isNull(source) ? defaultV : source;
    }

    /**
     * 获取系统时间加上8小时后的时间
     * 主要用于解决向mongo插入当前系统时间，查询时慢8小时的问题
     */
    public static Date getAfterEightHourDate() {
        Date now = new Date();
        // 加上8小时
        long eightHoursInMillis = TimeUnit.HOURS.toMillis(8);
        return new Date(now.getTime() + eightHoursInMillis);
    }

    /**
     * 根据map的值排序
     */
    public static LinkedHashMap<String, Integer> sortByValueDesc(Map<String, Integer> map) {
        LinkedHashMap<String, Integer> result = new LinkedHashMap<>(map.size());
        List<Map.Entry<String, Integer>> list = new ArrayList<>(map.entrySet());
        list.stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .forEach(e -> result.put(e.getKey(), e.getValue()));
        return result;
    }
}
