package com.yxt.lotprice.service.model.bo;

import com.yxt.lotprice.service.model.enums.AdjustRoleType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 调价申请单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
public class PriceAdjustFormSearchAuthOriginBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前用户id
     */
    private String userId;

    /**
     * 当前用户所属的机构id路径
     */
    private String orgIdPath;

    /**
     * 当前用户所属分公司
     */
    private String companyCode;

    /**
     * 员工职能类别所属机构编码（心云调价系统配置的角色和机构映射关系中的机构编码）
     */
    private String approverOrgCode;

    /**
     * 角色编码
     */
    private AdjustRoleType roleCode;


    /**
     * 1.角色编码+分公司编码可以定位到审批流（分公司级）
     * 2.针对一个分公司下的分部/区域的审核人员，再加上approverOrgCode可以确定当前用户是否有权限审批
     */
}
