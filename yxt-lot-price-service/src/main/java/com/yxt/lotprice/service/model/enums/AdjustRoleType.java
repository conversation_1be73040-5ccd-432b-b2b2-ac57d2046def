package com.yxt.lotprice.service.model.enums;

import com.alibaba.fastjson.JSON;
import lombok.Getter;

@Getter
public enum AdjustRoleType{
    //审批人
    STORE_S_1("门店店长", AdjustScopeType.COMPANY, AdjustRoleDimensionType.SUBMITTER,1,0),
    AREA_S_1("区域经理", AdjustScopeType.COMPANY, AdjustRoleDimensionType.SUBMITTER,2,0),
    BRANCH_S_1("分布运营管理部", AdjustScopeType.COMPANY, AdjustRoleDimensionType.SUBMITTER,3,0),
    COMPANY_S_1("子公司商品部", AdjustScopeType.COMPANY, AdjustRoleDimensionType.SUBMITTER,6,0),
    COMPANY_S_2("子公司采购部", AdjustScopeType.COMPANY, AdjustRoleDimensionType.SUBMITTER,6,0),
    COMPANY_S_3("子公司运营管理部", AdjustScopeType.COMPANY, AdjustRoleDimensionType.SUBMITTER,6,0),
    COMPANY_S_4("子公司品类推广部", AdjustScopeType.COMPANY, AdjustRoleDimensionType.SUBMITTER,6,0),
    GROUP_S_1("集团商品中心", AdjustScopeType.GROUP, AdjustRoleDimensionType.SUBMITTER,1,0),
    GROUP_S_2("鸿翔中药公司商品管理部", AdjustScopeType.GROUP, AdjustRoleDimensionType.SUBMITTER,1,0),
    GROUP_S_3("集团采购中心", AdjustScopeType.GROUP, AdjustRoleDimensionType.SUBMITTER,1,0),
    GROUP_S_4("集团非药事业部", AdjustScopeType.GROUP, AdjustRoleDimensionType.SUBMITTER,1,0),
    GROUP_S_5("集团后台人员", AdjustScopeType.GROUP, AdjustRoleDimensionType.SUBMITTER,1,0),
    GROUP_S_6("一心到家科技公司", AdjustScopeType.GROUP, AdjustRoleDimensionType.SUBMITTER,1,0),

    //报批人
    SUPERIOR_A_10("申请人直接上级", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,1,1),
    BRANRCH_A_1("分部运营管理部经理", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,4,1),
    BRANRCH_A_2("分部总经理", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,5,1),

    COMPANY_A_1("子公司物价管理专人", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,6,0),
    COMPANY_A_2("子公司商品部经理", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,6,0),
    COMPANY_A_3("子公司商品总监", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,6,0),
    COMPANY_A_4("子公司供应链总监", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,6,0),
    COMPANY_A_5("子公司总经理", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,6,0),
    COMPANY_A_6("上级子公司总经理", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,6,0),

    SUPERIOR_A_20("申请人直接上级（主管除外）", AdjustScopeType.GROUP, AdjustRoleDimensionType.APPROVER,7,1),
    GROUP_A_1("申请人中心总监", AdjustScopeType.GROUP, AdjustRoleDimensionType.APPROVER,7,1),
    GROUP_A_2("申请人分管总监", AdjustScopeType.GROUP, AdjustRoleDimensionType.APPROVER,7,1),
    GROUP_A_3("集团中心商品总监", AdjustScopeType.GROUP, AdjustRoleDimensionType.APPROVER,7,0),
    GROUP_A_4("集团中心品类规划部部长", AdjustScopeType.GROUP, AdjustRoleDimensionType.APPROVER,7,0),
    GROUP_A_5("集团运营中心营销管理部部长", AdjustScopeType.GROUP, AdjustRoleDimensionType.APPROVER,7,0);



    AdjustRoleType(String name, AdjustScopeType scope, AdjustRoleDimensionType roleDimensionType, int order, int authCheckSubOrg) {
        this.nameCh = name;
        this.scope = scope;
        this.roleDimensionType = roleDimensionType;
        this.order = order;
        this.authCheckSubOrg = authCheckSubOrg;
    }

    /**
     * 中文名
     */
    private String nameCh;
    /**
     * 角色属性
     */
    private AdjustScopeType scope;
    /**
     *
     */
    private AdjustRoleDimensionType roleDimensionType;

    /**
     * 排序
     */
    private int order;

    /**
     * 角色权限检查是否需要校验机构信息
     * 如：公司级-分部运营管理部经理和分部总经理
     * 如：集团级-申请人中心总监和申请人分管总监
     */

    private int authCheckSubOrg;

    public static AdjustRoleType getByName(String name) {
        for (AdjustRoleType value : AdjustRoleType.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }



}
