package com.yxt.lotprice.service.cache;

import cn.hutool.core.collection.CollUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lotprice.service.manager.sdk.BaseInfoClient;
import com.yxt.lotprice.service.manager.sdk.dto.QueryStoreDTO;
import com.yxt.lotprice.service.manager.sdk.dto.StoreDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: mengzilei
 * @Date: 2024-05-13  11:33
 * 全量门店信息缓存
 */

@Component
@Slf4j
public class AllStoreInfoCache implements InitializingBean, DisposableBean {

    // 所有门店key
    private static final String ALL_STORE_KEY = "ALL_STORE";

    @Resource
    private BaseInfoClient baseInfoClient;


    private LoadingCache<String, Map<String, StoreDTO>> allStoreInfoCache;


    public Map<String, StoreDTO> queryAllStoreInfoCache() {
        return allStoreInfoCache.get(ALL_STORE_KEY);
    }


    @Override
    public void afterPropertiesSet() {
        allStoreInfoCache = Caffeine.newBuilder()
                // 设置过期时间
                .expireAfterWrite(360, TimeUnit.MINUTES).refreshAfterWrite(350, TimeUnit.MINUTES)
                .initialCapacity(4)
                // 缓存如果没有，loadingcache会自动加zai
                // 缓存key的最大个数
                .maximumSize(16)
                .build(key -> loadCache());


    }


    @Override
    public void destroy() throws Exception {
        // 关闭缓存
        if (Objects.nonNull(allStoreInfoCache)) {
            allStoreInfoCache.cleanUp();
        }
    }


    private Map<String, StoreDTO> loadCache() {
        log.info("所有门店缓存加载");
        QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setMerCode("500001");
        try {
            ResponseBase<List<StoreDTO>> listResponseBase = baseInfoClient.listAllV2(queryStoreDTO);
            if (!listResponseBase.checkSuccess() || CollectionUtils.isEmpty(listResponseBase.getData())) {
                log.error("调用BaseInfo查询门店信息异常,加载缓存失败");
                throw new YxtBizException("加载门店缓存失败");
            }
            List<StoreDTO> stores = listResponseBase.getData();
            return stores.stream().collect(Collectors.toMap(StoreDTO::getStCode, StoreDTO -> StoreDTO));
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    public StoreDTO getStore(String stCode) {
        Map<String, StoreDTO> allStoreMap = allStoreInfoCache.get(ALL_STORE_KEY);
        if (CollUtil.isNotEmpty((allStoreMap))) {
            return allStoreMap.get(stCode);
        } else {
            //
            log.error("本地缓存中不存在门店信息storeCode:{}", stCode);
            return null;
        }
    }

    /**
     * 先从缓存获取，缓存中不存在再调用接口获取
     *
     * @param stCode 门店编码
     * @return
     */
    public StoreDTO getStoreCacheOrRemote(String stCode) {
        StoreDTO store = getStore(stCode);
        if (store == null) {
            QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
            queryStoreDTO.setMerCode("500001");
            queryStoreDTO.setStCode(stCode);
            ResponseBase<List<StoreDTO>> listResponseBase = baseInfoClient.listAllV2(queryStoreDTO);
            if (!listResponseBase.checkSuccess() || CollectionUtils.isEmpty(listResponseBase.getData())) {
                return null;
            }
            return listResponseBase.getData().get(0);
        }

        return store;
    }

    public StoreDTO getStoreByStoreId(String storeId) {
        Map<String, StoreDTO> allStoreMap = allStoreInfoCache.get(ALL_STORE_KEY);
        if (CollUtil.isNotEmpty((allStoreMap))) {
            return allStoreMap.values().stream().filter(StoreDTO -> StoreDTO.getId().equals(storeId)).findFirst().orElse(null);
        } else {
            //
            log.error("本地缓存中不存在门店信息storeId:{}", storeId);
            return null;
        }
    }

    /**
     * 返回值key为storeId的map
     *
     * @param storeId
     * @return
     */
    public Map<String, StoreDTO> getStoreByStoreIdList(List<String> storeId) {
        Map<String, StoreDTO> allStoreMap = allStoreInfoCache.get(ALL_STORE_KEY);
        if (CollUtil.isNotEmpty((allStoreMap))) {
            return allStoreMap.values().stream().filter(StoreDTO -> storeId.contains(StoreDTO.getId())).collect(Collectors.toMap(StoreDTO::getId, StoreDTO -> StoreDTO));
        } else {
            log.error("本地缓存中不存在门店信息storeId:{}", storeId);
            return null;
        }
    }

    public List<StoreDTO> getAllStore() {
        Map<String, StoreDTO> allStoreMap = allStoreInfoCache.get(ALL_STORE_KEY);
        if (CollUtil.isNotEmpty((allStoreMap))) {
            return new ArrayList<>(allStoreMap.values());
        } else {
            //
            log.error("本地缓存中不存在门店信息");
            return null;
        }
    }

    /**
     * 查询一个价格组的所有门店
     * @param priceGroup 价格组编码
     * @return allStoreList
     */
    public List<StoreDTO> getAllStoreByPriceGroup(String priceGroup) {
        if (StringUtils.isEmpty(priceGroup)) {
            throw new YxtBizException("价格组编码为空");
        }
        List<StoreDTO> allStoreList = getAllStore();
        if (CollectionUtils.isNotEmpty(allStoreList)) {
            return allStoreList.stream()
                    .filter(store -> priceGroup.equals(store.getPricegroupIdNumber()))
                    .collect(Collectors.toList());
        } else{
            return Collections.emptyList();
        }
    }

    public List<StoreDTO> getAllFranchiseStore() {
        Map<String, StoreDTO> allStoreMap = allStoreInfoCache.get(ALL_STORE_KEY);
        if (CollUtil.isNotEmpty((allStoreMap))) {
            return allStoreMap.values().stream().filter(StoreDTO::jmStore).collect(Collectors.toList());
        } else {
            //
            log.error("本地缓存中不存在门店信息");
            return null;
        }
    }

    /**
     * 查询所有加盟店门店编码
     */
    public List<String> getAllFranchiseStoreCodes() {
        Map<String, StoreDTO> storeMap = allStoreInfoCache.get(ALL_STORE_KEY);
        if (CollUtil.isEmpty(storeMap)) {
            return Collections.emptyList();
        }
        return storeMap.values().stream().filter(StoreDTO::jmStore).map(StoreDTO::getStCode).collect(Collectors.toList());
    }

    /**
     * 返回值 key为storeCode的map
     *
     * @param storeId
     * @return
     */
    public Map<String, StoreDTO> getStoreReturnKeyStCode(List<String> storeId) {
        Map<String, StoreDTO> allStoreMap = allStoreInfoCache.get(ALL_STORE_KEY);
        if (CollUtil.isNotEmpty((allStoreMap))) {
            return allStoreMap.values().stream().filter(StoreDTO -> storeId.contains(StoreDTO.getId())).collect(Collectors.toMap(StoreDTO::getStCode, StoreDTO -> StoreDTO));
        } else {
            log.error("本地缓存中不存在门店信息storeId:{}", storeId);
            return null;
        }
    }
}
