package com.yxt.lotprice.service.utils;

import cn.hutool.core.collection.CollectionUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.FatalBeanException;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2022/5/15 23:59
 */
public class BeanUtil {

    public static <T, V> List<T> copyList(List<V> list, Class<T> clazz) {
        List<T> targetList = new ArrayList<T>();
        if (CollectionUtils.isEmpty(list)) {
            return targetList;
        }
        T target = null;
        for (V source : list) {
            try {
                target = clazz.newInstance();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            BeanUtils.copyProperties(source, target);
            targetList.add(target);
        }
        return targetList;
    }

    public static <T, V> List<T> copyList(List<V> list, Class<T> clazz, BeanMapper<T, V> beanMapper) {
        List<T> targetList = new ArrayList<T>();
        if (CollectionUtils.isEmpty(list)) {
            return targetList;
        }
        T target = null;
        for (V source : list) {
            try {
                target = clazz.newInstance();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            BeanUtils.copyProperties(source, target);
            beanMapper.map(target, source);
            targetList.add(target);
        }
        return targetList;
    }

    public static void copyProperties(Object source, Object target) {
        BeanUtils.copyProperties(source, target);
    }

    public static <T, V> void copyProperties(V source, T target, BeanMapper<T, V> beanMapper) {
        BeanUtils.copyProperties(source, target);
        beanMapper.map(target, source);
    }

    public static void copyProperties(Object source, Object target, Boolean skipNull) {
        if (skipNull && (source == null || target == null)) {
            return;
        }
        BeanUtils.copyProperties(source, target);
    }

    public static <T> T copyProperties(Object source, Class<T> target) {
        T t;
        try {
            t = target.newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (source != null) {
            BeanUtils.copyProperties(source, t);
        }
        return t;
    }

    public static void copyTargetProperties(Object source, Object target, List<String> fields) {
        if (source == null || target == null || CollectionUtil.isEmpty(fields)) {
            return;
        }
        Class<?> sourceClass = source.getClass();
        Class<?> targetClass = target.getClass();
        for (String field : fields) {
            PropertyDescriptor targetPd = BeanUtils.getPropertyDescriptor(targetClass, field);
            if (targetPd != null) {
                Method writeMethod = targetPd.getWriteMethod();
                if (writeMethod != null) {
                    PropertyDescriptor sourcePd = BeanUtils.getPropertyDescriptor(sourceClass, targetPd.getName());
                    if (sourcePd != null) {
                        Method readMethod = sourcePd.getReadMethod();
                        if (readMethod != null &&
                                ClassUtils.isAssignable(writeMethod.getParameterTypes()[0], readMethod.getReturnType())) {
                            try {
                                if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                                    readMethod.setAccessible(true);
                                }
                                Object value = readMethod.invoke(source);
                                if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                                    writeMethod.setAccessible(true);
                                }
                                writeMethod.invoke(target, value);
                            } catch (Throwable ex) {
                                throw new FatalBeanException(
                                        "Could not copy property '" + targetPd.getName() + "' from source to target", ex);
                            }
                        }
                    }
                }
            }
        }

    }

}
