package com.yxt.lotprice.service.manager.iface;

import com.yxt.lotprice.service.model.bo.PriceResultsBO;
import com.yxt.lotprice.service.model.bo.PriceResultsUpdateBO;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import com.yxt.lotprice.service.model.enums.log.PriceSourceEnum;

import java.util.Collection;
import java.util.List;

/**
 * Since: 2025/05/23 14:15
 * Author: qs
 */
public interface PriceAdjustResultsManager {

    /**
     * 查询
     * @param tableName 不为空则直接使用计算出的表名称
     * @param companyCodes 公司编码
     * @param storeCodes 门店编码集合
     * @param erpCode 商品编码
     * @param priceType 价格类型
     * @return 价格数据
     */
    List<PriceResultsBO> listByErpCodePriceTypeStoreCode(String tableName, Collection<String> companyCodes, Collection<String> storeCodes, String erpCode, PriceTypeEnum priceType);

    /**
     * 查询单门店的调价结果
     * @param companyCode 公司编码
     * @param storeCode 门店编码集合
     * @param priceType 价格类型
     * @param erpCodes 商品编码
     * @return 价格数据
     */
    List<PriceResultsBO> listByStoreCodePriceTypeErpCodes(String companyCode, String storeCode, PriceTypeEnum priceType, Collection<String> erpCodes);

    /**
     * 批量保存数据
     * @param inserts 新增数据
     */
    void saveBatch(Collection<PriceResultsBO> inserts);

    /**
     * 批量更新价格代和价格
     * 更新当前价格、下次价格、价格代
     * @param updates 待更新数据
     */
    void updatePriceSegment(Collection<PriceResultsUpdateBO> updates, PriceSourceEnum sourceEnum);

    /**
     * 批量更新价格代，只更新价格代不更新价格
     * 更新当前价格、下次价格、价格代
     * @param updates 待更新数据
     */
    void updatePriceSegmentOnly(Collection<PriceResultsUpdateBO> updates, PriceSourceEnum sourceEnum);

    /**
     * 批量更新价格
     * 更新当前价格、下次价格
     * @param updates 待更新数据
     */
    void updatePrice(Collection<PriceResultsUpdateBO> updates, PriceSourceEnum sourceEnum);
}
