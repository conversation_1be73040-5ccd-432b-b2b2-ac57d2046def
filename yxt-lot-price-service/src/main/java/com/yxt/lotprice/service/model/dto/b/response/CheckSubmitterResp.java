package com.yxt.lotprice.service.model.dto.b.response;

import com.yxt.lotprice.service.model.dto.b.request.ItemRelateApproverReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22 11:30
 */
@Data
@ApiModel("报批人是否重复结果")
public class CheckSubmitterResp {
    @ApiModelProperty("报批人组织是否重复")
    private Boolean isRepeat;

    @ApiModelProperty("报批人组织重复的配置")
    private List<RepeatItemDTO> repeatItemList;
}
