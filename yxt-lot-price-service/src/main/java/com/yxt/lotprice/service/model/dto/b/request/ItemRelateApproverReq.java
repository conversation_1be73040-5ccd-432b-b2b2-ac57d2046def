package com.yxt.lotprice.service.model.dto.b.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 18:19
 */
@Data
@ApiModel("审批人组织配置")
public class ItemRelateApproverReq {
    @ApiModelProperty("职能类别编码")
    private String roleCode;

    @ApiModelProperty("员工关联信息")
    private List<RelateExtReq> relateExtList;
}
