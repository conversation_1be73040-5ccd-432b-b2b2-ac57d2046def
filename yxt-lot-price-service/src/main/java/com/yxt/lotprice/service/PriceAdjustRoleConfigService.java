package com.yxt.lotprice.service;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.model.dto.b.request.*;
import com.yxt.lotprice.service.model.dto.b.response.*;
import com.yxt.lotprice.service.model.enums.AdjustRoleType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:18
 */
public interface PriceAdjustRoleConfigService {

    PageDTO<PriceAdjustRangePageResp> priceAdjustRangePage(PriceAdjustRangePageReq req);

    PageDTO<SubmitterPageResp> submitterPage(SubmitterPageReq req);

    Boolean addSubmitter(AddOrEditSubmitterReq req,String userName);

    Boolean addApprover(AddOrEditApproverReq req, String userName);

    PageDTO<ApproverPageResp> approverPage(ApproverPageReq req);

    ApproverDetailResp approverDetail(String code);

    SubmitterDetailResp submitterDetail(String code);

    List<SelectorDTO> roleSelector(RoleSelectorReq req);

    List<SelectorDTO> priceTypeSelector();

    List<SelectorDTO> companySelector();

    List<SelectorDTO> dimensionSelector();

    CheckSubmitterResp checkSubmitter(AddOrEditSubmitterReq req);

    LocateRoleConfigResp locateRoleConfig(LocateRoleConfigReq req);

    PageDTO<ToEditPageResp> queryToEditPage(ToEditPageReq req);

    PageDTO<UserDTO> userPage(UserPageReq req);

    AdjustRoleType getCurrentUserRoleType(String orCode);
}
