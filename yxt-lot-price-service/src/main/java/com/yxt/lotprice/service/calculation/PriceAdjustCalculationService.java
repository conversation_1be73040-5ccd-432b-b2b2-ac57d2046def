package com.yxt.lotprice.service.calculation;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemDetailBO;
import com.yxt.lotprice.service.model.bo.PriceResultsBO;
import com.yxt.lotprice.service.model.bo.PriceTimelineSegmentBO;
import com.yxt.lotprice.service.model.dto.b.request.QueryPriceAdjustResultsReq;
import com.yxt.lotprice.service.model.dto.b.response.QueryPriceAdjustResultsResp;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import com.yxt.lotprice.service.model.enums.log.PriceSourceEnum;

import java.util.List;

/**
 * 价格调整计算Service接口
 * 
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface PriceAdjustCalculationService {

    /**
     * 处理调价单项目
     * 
     * @param formItemId 调价单项目ID
     * @return 处理结果
     */
    void processAdjustPriceFormItem(Long formItemId, PriceSourceEnum priceSourceEnum);

    /**
     * 计算门店商品一种类型价格结果
     * @param priceTypeEnum 价格类型
     * @param storeCode 门店编码
     * @param erpCodeList 商品编码集合
     */
    List<PriceResultsBO> calculatePriceResult(PriceTypeEnum priceTypeEnum, String storeCode, List<String> erpCodeList);

    /**
     * 根据调价单价格明细计算价格结果
     * @param priceDetailList 门店所有价格明细
     * @return 一个门店一种类型的价格结果
     */
    List<PriceResultsBO> calculatePriceResult(List<PriceAdjustFormItemDetailBO> priceDetailList);

    /**
     * 根据一个门店商品一种类型价格的所有价格明细计算价格代
     * @param priceDetailList 一个门店 一种价格类型 一个商品的所有调价单价格明细
     * @return 一个商品的价格代
     */
    List<PriceTimelineSegmentBO> calculatePriceSegmentSingle(List<PriceAdjustFormItemDetailBO> priceDetailList);

    /**
     * 新增/更新结果数据 得到需要下发的数据
     * @param sourceEnum 来源
     * @param newList 新的数据
     * @param oldList 老数据
     * @return 待下发的数据
     */
    List<PriceResultsBO> saveOrUpdateAndGetPublishPrice(PriceSourceEnum sourceEnum, List<PriceResultsBO> newList, List<PriceResultsBO> oldList);
}
