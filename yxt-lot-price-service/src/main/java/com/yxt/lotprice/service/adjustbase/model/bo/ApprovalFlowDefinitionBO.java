package com.yxt.lotprice.service.adjustbase.model.bo;

import com.yxt.lotprice.service.model.enums.AdjustRoleType;
import com.yxt.lotprice.service.model.enums.AdjustStatusType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 调价审批流配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
public class ApprovalFlowDefinitionBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 编码
     */
    private String code;

    /**
     * 流程名称
     */
    private String name;

    /**
     * 项目属性-子公司/ 集团
     */
    private String scope;

    /**
     * 子公司编码
     */
    private String companyCode;

    /**
     * 定价/调价
     */
    private String adjustType;

    /**
     * 短流程/长流程
     */
    private String flowType;

    /**
     * 1-启用；0-禁用
     */
    private AdjustStatusType status;

    /**
     * 节点配置，json字符串，审核流程节点以及对应的人的角色
     */
    private List<ApprovalFlowNodeItemBO> configNodeList;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 修改人
     */
    private String modifyName;

    /**
     * 报批人角色
     */

    private AdjustRoleType  submiterRoleCode;

//    public List<ApprovalFlowNodeItemBO> configNodeList;


    @Data
    public static class ApprovalFlowNodeItemBO{
        @ApiModelProperty("序号:一审，二审，三审")
        private String code;

        @ApiModelProperty("id")
        private int id;

        @ApiModelProperty("name")
        private String name;

        @ApiModelProperty("审批人职能类别")
        private AdjustRoleType roleCode;

        @ApiModelProperty("备注")
        private String desc;
    }
}
