package com.yxt.lotprice.service.model.dto.b.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/26 15:41
 */
@Data
@ApiModel("调价单商品信息")
public class PriceAdjustFormItemDTO {
    @ApiModelProperty("机构编码")
    private String orgCode;
    @ApiModelProperty("机构名称")
    private String orgName;
    @ApiModelProperty("商品编码")
    private String erpCode;
    @ApiModelProperty("商品名称")
    private String erpName;
    @ApiModelProperty("规格型号")
    private String spec;
    @ApiModelProperty("生产厂家")
    private String manufacture;
    @ApiModelProperty("基本单位")
    private String unit;
    @ApiModelProperty("中标价")
    private BigDecimal bidPrice;
    @ApiModelProperty("集采价格")
    private BigDecimal collectPrice;
    @ApiModelProperty("国谈价格")
    private BigDecimal countryPrice;
    @ApiModelProperty("市场竞对价格")
    private String marketPrice;

    @ApiModelProperty("竞对名称")
    private String competitorName;

    @ApiModelProperty("竞对价格")
    private String competitorPrice;

    @ApiModelProperty("竞对图片")
    private String competitorImg;

    @ApiModelProperty("零售价")
    private BigDecimal retailPrice;
    @ApiModelProperty("零售价开始时间")
    private LocalDate retailPriceBeginTime;
    @ApiModelProperty("零售价结束时间")
    private LocalDate retailPriceEndTime;
    @ApiModelProperty("会员价")
    private BigDecimal vipPrice;
    @ApiModelProperty("会员价开始时间")
    private LocalDate vipPriceBeginTime;
    @ApiModelProperty("会员价结束时间")
    private LocalDate vipPriceEndTime;
    @ApiModelProperty("慢病价")
    private BigDecimal chronicPrice;
    @ApiModelProperty("慢病价开始时间")
    private LocalDate chronicPriceBeginTime;
    @ApiModelProperty("慢病价结束时间")
    private LocalDate chronicPriceEndTime;
}
