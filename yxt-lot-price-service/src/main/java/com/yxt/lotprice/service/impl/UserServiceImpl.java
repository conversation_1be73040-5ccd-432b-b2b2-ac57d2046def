package com.yxt.lotprice.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lotprice.service.PriceAdjustRoleConfigService;
import com.yxt.lotprice.service.UserService;
import com.yxt.lotprice.service.adjustbase.model.bo.PriceAdjustRoleConfigBO;
import com.yxt.lotprice.service.cache.AllStoreInfoCache;
import com.yxt.lotprice.service.cache.AllUserInfoCache;
import com.yxt.lotprice.service.cache.OrgInfoCacheUtils;
import com.yxt.lotprice.service.manager.iface.PriceAdjustRoleConfigManager;
import com.yxt.lotprice.service.manager.sdk.dto.StoreDTO;
import com.yxt.lotprice.service.model.dto.b.response.CurrentUserCompanyResp;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationDTO;
import com.yxt.lotprice.service.model.enums.AdjustRoleType;
import com.yxt.lotprice.service.model.enums.AdjustScopeType;
import com.yxt.org.read.opensdk.emp.dto.response.EmployeeScrollResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 18:37
 */
@Service
@Slf4j
public class UserServiceImpl implements UserService {

    @Resource
    private AllUserInfoCache allUserInfoCache;

    @Resource
    private PriceAdjustRoleConfigService priceAdjustRoleConfigService;

    @Resource
    private PriceAdjustRoleConfigManager priceAdjustRoleConfigManager;

    @Resource
    private AllStoreInfoCache allStoreInfoCache;

    private static final String STORE_S_1 = "1009";
    private static final String AREA_S_1 = "1007";
    private static final String GROUP_S_5 = "yxtorgform05";
    @Override
    public AdjustRoleType getUserRoleType(String userId) {
        EmployeeScrollResDTO.EmployeeResDTO userInfo = allUserInfoCache.getUserInfo(userId);
        if (userInfo == null || userInfo.getId() == null){
            throw new YxtBizException("员工不存在");
        }
        if (STORE_S_1.equals(userInfo.getYxtSubgroups())){
            return AdjustRoleType.STORE_S_1;
        }
        if (AREA_S_1.equals(userInfo.getYxtSubgroups())){
            return AdjustRoleType.AREA_S_1;
        }
        String subOrgCode = userInfo.getSubOrgCode();
        SysOrganizationDTO sysOrganizationDTO = OrgInfoCacheUtils.getOrgByCode(subOrgCode);
        if (sysOrganizationDTO == null){
            throw new YxtBizException("员工所属部门不存在");
        }
        if (GROUP_S_5.equals(sysOrganizationDTO.getFormCode())){
            return AdjustRoleType.GROUP_S_5;
        }
        return priceAdjustRoleConfigService.getCurrentUserRoleType(sysOrganizationDTO.getOrCode());
    }

    @Override
    public PriceAdjustRoleConfigBO getUserRoleConfig(String userId,AdjustRoleType roleType) {
        EmployeeScrollResDTO.EmployeeResDTO userInfo = allUserInfoCache.getUserInfo(userId);
        if (userInfo == null){
            throw new YxtBizException("员工不存在");
        }
        PriceAdjustRoleConfigBO req = new PriceAdjustRoleConfigBO();
        req.setRoleCode(roleType.name());
        req.setCompanyCode(userInfo.getSubCompCode());
        List<PriceAdjustRoleConfigBO> result = priceAdjustRoleConfigManager.search(req);
        if (CollUtil.isNotEmpty(result)){
            return result.get(0);
        }
        return null;
    }

    @Override
    public StoreDTO getUserStore(String userId) {
        EmployeeScrollResDTO.EmployeeResDTO userInfo = allUserInfoCache.getUserInfo(userId);
        if (userInfo == null || userInfo.getId() == null){
            throw new YxtBizException("员工不存在");
        }
        return allStoreInfoCache.getStore(userInfo.getSubOrgCode());
    }

    @Override
    public List<CurrentUserCompanyResp> currentUserCompany(String userId) {
        AdjustRoleType userRoleType = getUserRoleType(userId);
        List<CurrentUserCompanyResp> currentUserCompanyResps = new ArrayList<>();
        if (AdjustScopeType.GROUP.equals(userRoleType.getScope())){
            List<SysOrganizationDTO> allUnitStore = OrgInfoCacheUtils.getAllUnitStore();
            if (CollUtil.isNotEmpty(allUnitStore)){
                for (SysOrganizationDTO sysOrganizationDTO : allUnitStore) {
                    CurrentUserCompanyResp currentUserCompanyResp = new CurrentUserCompanyResp();
                    currentUserCompanyResp.setCompanyCode(sysOrganizationDTO.getOrCode());
                    currentUserCompanyResp.setCompanyName(sysOrganizationDTO.getOrName());
                    currentUserCompanyResps.add(currentUserCompanyResp);
                }
            }
        }else {
            EmployeeScrollResDTO.EmployeeResDTO userInfo = allUserInfoCache.getUserInfo(userId);
            SysOrganizationDTO sysOrganizationDTO = OrgInfoCacheUtils.getOrgByCode(userInfo.getSubCompCode());
            if (sysOrganizationDTO != null){
                CurrentUserCompanyResp currentUserCompanyResp = new CurrentUserCompanyResp();
                currentUserCompanyResp.setCompanyCode(sysOrganizationDTO.getOrCode());
                currentUserCompanyResp.setCompanyName(sysOrganizationDTO.getOrName());
                currentUserCompanyResps.add(currentUserCompanyResp);
            }
        }
        return currentUserCompanyResps;
    }
}
