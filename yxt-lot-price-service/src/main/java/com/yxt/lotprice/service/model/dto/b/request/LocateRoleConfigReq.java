package com.yxt.lotprice.service.model.dto.b.request;

import com.yxt.lotprice.service.model.enums.AdjustRoleDimensionType;
import com.yxt.lotprice.service.model.enums.AdjustRoleType;
import com.yxt.lotprice.service.model.enums.AdjustScopeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/5/21 17:23
 */
@Data
@ApiModel(value = "调价范围配置定位请求参数")
public class LocateRoleConfigReq {
    @ApiModelProperty(value = "子公司/集团 ，子公司传子公司编码，集团传yxt")
    private String companyCode;

    @ApiModelProperty(value = "报批人")
    private AdjustRoleType role;
}
