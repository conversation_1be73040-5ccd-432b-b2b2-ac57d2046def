package com.yxt.lotprice.service.model.dto.b.response;

import com.yxt.lotprice.service.model.enums.AdjustFlowType;
import com.yxt.lotprice.service.model.enums.AdjustRoleType;
import com.yxt.lotprice.service.model.enums.AdjustType;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/21 17:23
 */
@Data
@ApiModel(value = "调价范围配置定位返回参数")
public class LocateRoleConfigResp {
    @ApiModelProperty(value = "子公司/集团 ，子公司传子公司编码，集团传yxt")
    private String companyCode;
    @ApiModelProperty(value = "审批人/报批人")
    private AdjustRoleType role;

    @ApiModelProperty(value = "定/调价类别    ADJUSTMENT(\"定价\"),\n" +
            "    PRICING(\"新品调价\"),\n" +
            "    GIFT_PRICING(\"赠品定价/调价\");")
    private List<SelectorDTO> typeList;

    @ApiModelProperty(value = "流程类型    LONG(\"长流程\"),\n" +
            "    SHORT(\"短流程\"),\n" +
            "    NONE(\"不区分长短流程\");")
    private List<SelectorDTO> flowTypeList;
}
