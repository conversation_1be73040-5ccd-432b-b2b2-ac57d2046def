package com.yxt.lotprice.service.mq.impl;

import com.alibaba.fastjson.JSON;
import com.yxt.lotprice.common.model.constant.MqTopicConstant;
import com.yxt.lotprice.common.util.PriceUtils;
import com.yxt.lotprice.service.manager.mq.MqSenderManager;
import com.yxt.lotprice.service.model.bo.PriceResultsBO;
import com.yxt.lotprice.service.model.dto.mq.rocketmq.StorePriceDTO;
import com.yxt.lotprice.service.mq.PricePublishService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Since: 2025/05/28 10:53
 * Author: qs
 */

@Service
public class PricePublishServiceImpl implements PricePublishService {

    @Resource
    private MqSenderManager rocketMqSenderManager;

    @Override
    public void publishPrice(List<StorePriceDTO> publishList) {
        rocketMqSenderManager.sendBatchMessages(publishList.stream().map(JSON::toJSONString).collect(Collectors.toList())
                , MqTopicConstant.TP_PUBLISH_PRICE, "");
    }

    @Override
    public List<StorePriceDTO> buildPublishData(List<PriceResultsBO> publishPriceResultList) {
        // todo qs 价格下发 重试组件 判断是否切换
        return publishPriceResultList.stream()
                // todo qs 是否在配置门店当中
                .filter(f -> true)
                // 按照门店分组
                .collect(Collectors.groupingBy(PriceResultsBO::getStoreCode))
                .entrySet()
                .stream()
                .map(entry -> {
                    String storeCode = entry.getKey();
                    List<PriceResultsBO> priceList = entry.getValue();

                    List<StorePriceDTO.GoodsPrice> goodsList = priceList.stream()
                            .map(r -> {
                                StorePriceDTO.GoodsPrice goodsPrice = new StorePriceDTO.GoodsPrice();
                                goodsPrice.setErpCode(r.getErpCode());
                                goodsPrice.setPriceType(r.getPriceType().name());

                                List<StorePriceDTO.GoodsPrice.Price> prices = new ArrayList<>();
                                if (r.getPrice() != null) {
                                    prices.add(new StorePriceDTO.GoodsPrice.Price(
                                            PriceUtils.roundToPriceString(r.getPrice()),
                                            r.getStartTime().toString(),
                                            r.getEndTime().toString()
                                    ));
                                    if (r.getNextPrice() != null) {
                                        prices.add(new StorePriceDTO.GoodsPrice.Price(
                                                PriceUtils.roundToPriceString(r.getNextPrice()),
                                                r.getNextStartTime().toString(),
                                                r.getNextEndTime().toString()
                                        ));
                                    }
                                }
                                goodsPrice.setPriceList(prices);
                                return goodsPrice;
                            })
                            .collect(Collectors.toList());

                    StorePriceDTO storePriceDTO = new StorePriceDTO();
                    storePriceDTO.setStoreCode(storeCode);
                    storePriceDTO.setTimestamp(String.valueOf(System.currentTimeMillis()));
                    storePriceDTO.setGoodsList(goodsList);

                    return storePriceDTO;
                })
                .collect(Collectors.toList());
    }

}
