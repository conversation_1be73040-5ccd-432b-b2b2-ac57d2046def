package com.yxt.lotprice.service.model.bo;

import com.yxt.lotprice.service.model.enums.AdjustRoleDimensionType;
import com.yxt.lotprice.service.model.enums.AdjustRoleType;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 调价申请单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
public class PriceAdjustRoleItemRelateSearchBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分公司编码，yxt-集团，1006-四川公司，...
     */
    private String companyCode;

    /**
     * 角色编码
     */
    private AdjustRoleType approverRoleCode;

    /**
     * 1.审批人审核的机构编码,roleType为APPROVER才可能有值，有值要做特殊处理
     * 2.当前用户所属机构编码
     */
    private String orgCode;

    /**
     * 当前用户id
     */
    private String userId;


    /**
     * 报批人角色编码
     */
    private AdjustRoleType submiterRoleCode;

    /**
     * 报批人角色维度类型
     */
    private AdjustRoleDimensionType dimensionType;


    /**
     * 当前用户所属角色类型
     */
    private AdjustRoleDimensionType roleType;


}
