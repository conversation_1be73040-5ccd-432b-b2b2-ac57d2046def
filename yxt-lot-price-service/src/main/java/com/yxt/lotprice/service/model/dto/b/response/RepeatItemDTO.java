package com.yxt.lotprice.service.model.dto.b.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/23 16:56
 */
@Data
@ApiModel(value = "重复项目")
public class RepeatItemDTO {
    @ApiModelProperty("组织编码")
    private String orCode;
    @ApiModelProperty("报批人类别")
    private String roleType;
    @ApiModelProperty("组织名称")
    private String orName;
}
