package com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.lang.dto.PageBase;
import com.yxt.lotprice.service.model.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("审批流分页查询返回对象")
public class ApprovalFlowPageResp{

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("审批流名称")
    private String name;

    @ApiModelProperty("审批流项目属性-集团/子公司    COMPANY(\"分公司\"),\n" +
            "    GROUP(\"集团\");")
    private AdjustScopeType scope;

    @ApiModelProperty("审批流项目属性-集团/子公司")
    private String scopeName;

    @ApiModelProperty("所属公司编码")
    private String companyCode;

    @ApiModelProperty("所属公司名称")
    private String companyName;

    @ApiModelProperty("报批人角色编码")
    private AdjustRoleType submiterRoleCode;

    @ApiModelProperty("报批人角色名称")
    private String submiterRoleName;

    @ApiModelProperty("定/调价类别-定价/调价    ADJUSTMENT(\"定价\"),\n" +
            "    PRICING(\"新品调价\"),\n" +
            "    GIFT_PRICING(\"赠品定价/调价\");")
    private AdjustType adjustType;
    @ApiModelProperty("定/调价类别-定价/调价")
    private String adjustTypeName;

    @ApiModelProperty("审批流流程类型    LONG(\"长流程\"),\n" +
            "    SHORT(\"短流程\"),\n" +
            "    NONE(\"不区分长短流程\");")
    private AdjustFlowType flowType;

    @ApiModelProperty("审批流流程类型")
    private String flowTypeName;

    @ApiModelProperty("修改人")
    private String modifyName;
    @ApiModelProperty("状态    ON,\n" +
            "    OFF;")
    private AdjustStatusType status;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyTime;

}
