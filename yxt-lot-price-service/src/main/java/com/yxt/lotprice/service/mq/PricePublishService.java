package com.yxt.lotprice.service.mq;

import com.yxt.lotprice.service.model.bo.PriceResultsBO;
import com.yxt.lotprice.service.model.dto.mq.rocketmq.StorePriceDTO;

import java.util.List;

/**
 * 价格下发
 * Since: 2025/05/28 10:51
 * Author: qs
 */
public interface PricePublishService {

    /**
     * 下发价格
     * @param publishList 价格数据
     */
    void publishPrice(List<StorePriceDTO> publishList);

    /**
     * 封装发送mq数据
     * @param publishPriceResultList 价格结果数据
     * @return mq 发送数据格式
     */
    List<StorePriceDTO> buildPublishData(List<PriceResultsBO> publishPriceResultList);
}
