package com.yxt.lotprice.service.calculation;

import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemDetailBO;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;

import java.util.Collection;
import java.util.List;

/**
 * Since: 2025/05/21 16:34
 * Author: qs
 */
public interface PriceAdjustFormItemDetailService {

    /**
     * 保存调价单明细数据到price_adjust_form_item_detail表
     *
     * @param formItemId 调价单项目ID
     * @return 是否成功
     */
    boolean listByStoreAndPriceGroup(String storeCode);

    /**
     * 保存调价单明细数据到price_adjust_form_item_detail表
     *
     * @param formItemId 调价单项目ID
     * @return 是否成功
     */
    boolean saveFormItemDetail();

    /**
     * 删除数据
     *
     * @param formItemId 调价单项目ID
     * @return 是否成功
     */
    boolean deleteByStoreAndPriceGroup(String storeCode);

    /**
     * 查询有效的调价单价格明细
     * @return 明细集合
     */
    List<PriceAdjustFormItemDetailBO> listEnableByStoreCodeErpCodes(PriceTypeEnum priceTypeEnum, String storeCode, Collection<String> erpCodes);
}
