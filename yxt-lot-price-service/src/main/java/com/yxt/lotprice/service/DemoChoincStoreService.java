package com.yxt.lotprice.service;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.model.dto.b.request.DemoBReq;
import com.yxt.lotprice.service.model.dto.b.response.DemoBResp;
import com.yxt.lotprice.service.model.dto.third.request.DemoThirdReq;
import com.yxt.lotprice.service.model.dto.third.response.DemoThirdResp;

import java.util.List;

public interface DemoChoincStoreService {
    /**
     * 分页查询-三方专用
     * @param req
     * @return
     */
    PageDTO<DemoThirdResp> page(DemoThirdReq req);

    /**
     * 列表查询-B端专用
     * @param req
     * @return
     */
    List<DemoBResp> list(DemoBReq req);
}
