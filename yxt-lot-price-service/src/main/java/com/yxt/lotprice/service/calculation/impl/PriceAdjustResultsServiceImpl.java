package com.yxt.lotprice.service.calculation.impl;

import com.yxt.lang.exception.YxtBizException;
import com.yxt.lotprice.service.cache.AllStoreInfoCache;
import com.yxt.lotprice.service.calculation.PriceAdjustResultsService;
import com.yxt.lotprice.service.manager.iface.PriceAdjustResultsManager;
import com.yxt.lotprice.service.manager.sdk.dto.StoreDTO;
import com.yxt.lotprice.service.model.bo.PriceResultsBO;
import com.yxt.lotprice.service.model.bo.PriceResultsUpdateBO;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import com.yxt.lotprice.service.model.enums.log.PriceSourceEnum;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;

/**
 * Since: 2025/05/22 19:08
 * Author: qs
 */

@Service
public class PriceAdjustResultsServiceImpl implements PriceAdjustResultsService {

    @Resource
    private AllStoreInfoCache allStoreInfoCache;
    @Resource
    private PriceAdjustResultsManager priceAdjustResultsManager;

    @Override
    public List<PriceResultsBO> listByErpCodePriceTypeStoreCode(String tableName, Collection<String> companyCodes, Collection<String> storeCodes, String erpCode, PriceTypeEnum priceType) {
        return priceAdjustResultsManager.listByErpCodePriceTypeStoreCode(tableName, companyCodes, companyCodes, erpCode, priceType);
    }

    @Override
    public List<PriceResultsBO> listByStoreCodePriceTypeErpCodes(String storeCode, PriceTypeEnum priceType, Collection<String> erpCodes) {
        StoreDTO storeDTO = allStoreInfoCache.getStore(storeCode);
        if (storeDTO == null) {
            throw new YxtBizException("收到门店新增消息，查询到门店信息为空");
        }
        return priceAdjustResultsManager.listByStoreCodePriceTypeErpCodes(storeDTO.getCompanyCode(), storeCode, priceType, erpCodes);
    }

    @Override
    public void saveBatch(List<PriceResultsBO> resultsBOList, PriceSourceEnum sourceEnum) {
        priceAdjustResultsManager.saveBatch(resultsBOList);
    }

    @Override
    public void updatePriceSegment(List<PriceResultsUpdateBO> resultsBOList, PriceSourceEnum sourceEnum) {
        priceAdjustResultsManager.updatePriceSegment(resultsBOList, sourceEnum);

    }

    @Override
    public void updatePriceSegmentOnly(List<PriceResultsUpdateBO> resultsBOList, PriceSourceEnum sourceEnum) {
        priceAdjustResultsManager.updatePriceSegmentOnly(resultsBOList, sourceEnum);
    }

    @Override
    public void updatePrice(List<PriceResultsUpdateBO> resultsBOList, PriceSourceEnum sourceEnum) {
        priceAdjustResultsManager.updatePrice(resultsBOList, sourceEnum);
    }


}
