package com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yxt.lotprice.service.model.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@ApiModel("审批流详情保存请求对象")
public class ApprovalFlowSaveDetailReq{

    @ApiModelProperty("id，有就新增，没有就更新")
    private Long id;

    @ApiModelProperty("审批流编码")
    private String code;

    @ApiModelProperty("审批流名称")
    @NotBlank(message = "审批流名称不能为空")
    @Size(max = 200, message = "审批名称长度不能超过200个字符")
    private String name;

    @ApiModelProperty("审批流项目属性-集团/子公司：COMPANY(\"分公司\"),\n" +
            "    GROUP(\"集团\")")
    @NotNull(message = "审批流项目属性-集团/子公司不能为空")
    private AdjustScopeType scope;

    @ApiModelProperty("所属公司编码")
    @NotNull(message = "所属公司编码不能为空")
    private String companyCode;

    @ApiModelProperty("提交人角色编码")
    @NotNull(message = "提交人角色编码不能为空")
    private AdjustRoleType submiterRoleCode;

    @ApiModelProperty("定/调价维度")
    @NotNull(message = "定/调价维度不能为空    ADJUSTMENT(\"定价\"),\n" +
            "    PRICING(\"新品调价\"),\n" +
            "    GIFT_PRICING(\"赠品定价/调价\");")
    private AdjustType adjustType;

    @ApiModelProperty("审批流类型")
    @NotNull(message = "审批流类型不能为空    LONG(\"长流程\"),\n" +
            "    SHORT(\"短流程\"),\n" +
            "    NONE(\"不区分长短流程\");")
    private AdjustFlowType flowType;

    @ApiModelProperty("状态")
    private AdjustStatusType status;

    @ApiModelProperty("审批流节点列表")
    @Size(min = 1, message = "审批流节点列表不能为空")
    private List<ApprovalFlowNodeItem> configNodeList;

    @Data
    @ApiModel("审批流节点配置信息")
    public static class ApprovalFlowNodeItem{
        @ApiModelProperty("序号:一审，二审，三审")
        private String code;

        @ApiModelProperty("id")
        private int id;

        @ApiModelProperty("name")
        private String name;

        @ApiModelProperty("审批人职能类别")
        private AdjustRoleType roleCode;

        @ApiModelProperty("备注")
        private String desc;
    }


    public static void main(String[] args) {
        ApprovalFlowSaveDetailReq detail = new ApprovalFlowSaveDetailReq();
        detail.setName("审批流1");
        detail.setFlowType(AdjustFlowType.SHORT);
        detail.setAdjustType(AdjustType.ADJUSTMENT);
        detail.setScope(AdjustScopeType.COMPANY);
        detail.setSubmiterRoleCode(AdjustRoleType.COMPANY_S_1);
        detail.setCompanyCode("1000");
        detail.setStatus(AdjustStatusType.ON);
        detail.setConfigNodeList(Lists.newArrayList());

        ApprovalFlowNodeItem item = new ApprovalFlowNodeItem();
        item.setCode("一审");
        item.setId(1);
        item.setName("一审名称");
        item.setRoleCode(AdjustRoleType.COMPANY_A_1);
        item.setRoleCode(AdjustRoleType.COMPANY_A_2);
        detail.getConfigNodeList().add(item);

        ApprovalFlowNodeItem item2 = new ApprovalFlowNodeItem();
        item2.setCode("二审");
        item2.setId(2);
        item2.setName("二审名称");
        item2.setRoleCode(AdjustRoleType.COMPANY_A_3);
        item2.setRoleCode(AdjustRoleType.COMPANY_A_4);
        detail.getConfigNodeList().add(item2);
        System.out.println(JSON.toJSONString(detail));

    }

}
