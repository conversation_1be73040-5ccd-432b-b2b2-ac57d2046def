package com.yxt.lotprice.service.manager.iface;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.model.bo.PriceAdjustRoleItemRelateBO;
import com.yxt.lotprice.service.model.dto.b.request.AddOrEditApproverReq;
import com.yxt.lotprice.service.model.dto.b.request.AddOrEditSubmitterReq;
import com.yxt.lotprice.service.model.dto.b.request.ItemRelateApproverReq;
import com.yxt.lotprice.service.model.dto.b.request.ToEditPageReq;
import com.yxt.lotprice.service.model.dto.b.response.*;
import com.yxt.lotprice.service.model.enums.AdjustRoleType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/20 11:53
 */
public interface PriceAdjustRoleItemRelateManager {

    Boolean addSubmitterItem(AddOrEditSubmitterReq req, String userName);

    Boolean updateSubmitterItem(AddOrEditSubmitterReq req, String userName);

    Boolean addApproverItem(AddOrEditApproverReq req, String userName);

    Boolean updateApproverItem(AddOrEditApproverReq req, String userName);

    List<ItemRelateSubmitterResp> getSubmitterItemList(String code,String scopeName);

    List<RepeatItemDTO> checkSubmitterList(String roleCode, List<String> orgCode);

    /**
     * 获取分公司或集团对应职能类别下的组织
     */
    PageDTO<ToEditPageResp> getSubmitterOrgList(ToEditPageReq req);

    /**
     * 获取已配置的员工信息
     * @param code
     * @param roleType
     * @return
     */
    List<RelateExtDTO> getItemRelateExtList(String code, AdjustRoleType roleType,List<String> orCodeList);

    List<ItemRelateApproverResp> getApproverItemList(String code);

    List<PriceAdjustRoleItemRelateBO> getRoleItemRelateByAppoverUserId(String userId);

    AdjustRoleType getCurrentUserRoleType(String orCode);
}
