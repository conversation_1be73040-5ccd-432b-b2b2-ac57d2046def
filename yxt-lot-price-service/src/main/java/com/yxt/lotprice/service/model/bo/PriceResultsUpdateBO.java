package com.yxt.lotprice.service.model.bo;

import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import com.yxt.lotprice.service.model.enums.PushStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Since: 2025/05/27 12:12
 * Author: qs
 */

@Data
public class PriceResultsUpdateBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * ERP商品编码
     */
    private String erpCode;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 分公司编码
     */
    private String companyCode;

    /**
     * 价格类型：（会员价，零售价，慢病价）
     */
    private PriceTypeEnum priceType;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 价格生效开始时间 包含
     */
    private LocalDate startTime;

    /**
     * 价格生效结束时间 包含
     */
    private LocalDate endTime;

    /**
     * 下一次价格
     */
    private BigDecimal nextPrice;

    /**
     * 下一次价格生效开始时间 包含
     */
    private LocalDate nextStartTime;

    /**
     * 下一次价格生效结束时间 包含
     */
    private LocalDate nextEndTime;

    /**
     * 推送 POS 状态
     */
    private PushStatusEnum pushPosStatus;

    /**
     * 推送 POS 记录
     */
    private String pushPosLog;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 价格时间段
     */
    private List<PriceTimelineSegmentBO> priceResultSegment;

    /**
     * 原价格
     */
    private PriceBO oldPrice;

    /**
     * 业务数据唯一key
     */
    public String buildUniqueKey() {
        return this.getErpCode() + "|" + this.getStoreCode() + "|" + this.getCompanyCode() + "|" + this.getPriceType();
    }

}
