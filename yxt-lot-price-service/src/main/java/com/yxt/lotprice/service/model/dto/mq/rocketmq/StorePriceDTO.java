package com.yxt.lotprice.service.model.dto.mq.rocketmq;

import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Since: 2025/05/27 15:41
 * Author: qs
 */

@Data
public class StorePriceDTO {

    /**
     * 时间戳
     */
    private String timestamp;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 商品信息
     */
    private List<GoodsPrice> goodsList;

    @Data
    public static class GoodsPrice {
        /**
         * 商品编码
         */
        private String erpCode;

        /**
         * 价格类型
         */
        private String priceType;

        /**
         * 价格段数据
         */
        private List<Price> priceList;

        @Data
        @AllArgsConstructor
        public static class Price {

            // 价格
            private String price;

            // 生效开始时间
            private String startTime;

            // 生效结束时间
            private String endTime;
        }
    }
}
