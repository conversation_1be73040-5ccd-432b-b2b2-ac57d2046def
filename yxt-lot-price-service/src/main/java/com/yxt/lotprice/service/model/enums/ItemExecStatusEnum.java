package com.yxt.lotprice.service.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Since: 2025/05/21 15:57
 * Author: qs
 */

@Getter
@AllArgsConstructor
public enum ItemExecStatusEnum {

    NOT_STARTED("未开始"),
    IN_PROGRESS("进行中"),
    COMPLETED("已完成"),
    FAILED("失败"),
    ;

    private String value;
    private final String desc;

    ItemExecStatusEnum(String desc) {
        this.desc = desc;
    }
}
