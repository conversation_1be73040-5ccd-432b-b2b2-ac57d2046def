package com.yxt.lotprice.service.model.bo.log;

import com.yxt.lotprice.service.model.bo.PriceBO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Since: 2025/05/27 12:03
 * Author: qs
 */

@Data
public class PriceChangeLogBO {

    /**
     * 价格计算来源
     */
    private String source;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 原价格
     */
    private PriceBO oldPrice;

    /**
     * 新价格
     */
    private PriceBO newPrice;

}
