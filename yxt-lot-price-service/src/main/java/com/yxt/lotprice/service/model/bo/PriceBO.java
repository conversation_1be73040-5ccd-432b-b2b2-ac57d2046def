package com.yxt.lotprice.service.model.bo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Since: 2025/05/27 14:20
 * Author: qs
 */

@Data
@AllArgsConstructor
public class PriceBO {

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 价格生效开始时间 包含
     */
    private LocalDate startTime;

    /**
     * 价格生效结束时间 包含
     */
    private LocalDate endTime;

    /**
     * 下一次价格
     */
    private BigDecimal nextPrice;

    /**
     * 下一次价格生效开始时间 包含
     */
    private LocalDate nextStartTime;

    /**
     * 下一次价格生效结束时间 包含
     */
    private LocalDate nextEndTime;
}
