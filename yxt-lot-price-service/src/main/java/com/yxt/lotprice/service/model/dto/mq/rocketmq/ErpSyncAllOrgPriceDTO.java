package com.yxt.lotprice.service.model.dto.mq.rocketmq;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
public class ErpSyncAllOrgPriceDTO {    /**
 * 商户编码
 */
@ApiModelProperty("商户编码")
@NotBlank(message = "商户编码 不能为空")
private String merCode;

    @ApiModelProperty("价格信息")
    @NotEmpty(message = "价格信息 不能为空")
    private List<PriceInfo> priceInfos;

    @ApiModelProperty("来源")
    private String source = "DERP";

    private String transferTime;


    @Data
    public static class PriceInfo implements Serializable {

        private static final long serialVersionUID = -6314666783936001832L;
        @ApiModelProperty("商品编码")
        @NotBlank(message = "商品编码 不能为空")
        @JsonAlias("material")
        private String erpCode;

        @ApiModelProperty("门店/仓库编码")
        @NotBlank(message = "门店/仓库编码 不能为空")
        @JsonAlias("org")
        private String storeCode;

        @ApiModelProperty("成本：存货核算-报表分析-存货收发存汇总表（期末结存-单价）")
        @JsonAlias("movingAvgPrice")
        private BigDecimal verpr;

        @ApiModelProperty("最高限价")
        @JsonAlias("maxLimitedPrice")
        private BigDecimal zzgxj;

        @ApiModelProperty("零售价")
        @JsonAlias("refRetailPrice")
        private BigDecimal wgbez;

        @ApiModelProperty("会员价")
        private BigDecimal vipPrice;

        private String updateDate;
    }
}
