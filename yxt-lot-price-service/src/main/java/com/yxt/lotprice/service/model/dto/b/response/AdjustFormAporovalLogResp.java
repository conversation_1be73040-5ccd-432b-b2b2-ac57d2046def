package com.yxt.lotprice.service.model.dto.b.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/29 10:15
 */
@Data
@ApiModel("调价单审批记录")
public class AdjustFormAporovalLogResp {
    @ApiModelProperty("审批通过价格")
    private BigDecimal aporovalPrice;
    @ApiModelProperty("审批通过调/定价开始时间")
    private LocalDateTime aporovalStartTime;
    @ApiModelProperty("审批通过调/定价结束时间")
    private LocalDateTime aporovalEndTime;
    @ApiModelProperty("审批备注")
    private String descs;
    @ApiModelProperty("审批结果 1:同意 0:不同意")
    private Integer aporovalStatus;

    @ApiModelProperty("审批人")
    private String aporovalUserName;
    @ApiModelProperty("审批人工号")
    private String aporovalUserCode;
}
