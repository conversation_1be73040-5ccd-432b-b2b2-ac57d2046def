package com.yxt.lotprice.service.manager.sdk.dto;

import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Collection;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class QueryStoreDTO extends PageBase {
    @ApiModelProperty(value = "商家编码")
    @NotEmpty
    private String merCode;
    @ApiModelProperty(value = "门店编码")
    private String stCode;
    @ApiModelProperty(value = "门店编码list")
    private List<String> stCodeList;
    private String userId;

    @ApiModelProperty(value = "是否上线门店（0非上线门店，1上线门店）")
    private Integer onlineStatus;

    @ApiModelProperty(value = "状态（0停用，1启用）")
    private Integer status;

    @ApiModelProperty(value = "仓库类型 0-不是仓库，1-普通仓库，2-门店仓库，3-供货商仓库")
    private Integer dcType;

    @ApiModelProperty(value = "搜索关键字，用于通过门店编码/门店名称模糊查询")
    private String searchKey;

    @ApiModelProperty(value = "是否有电商云线上门店")
    private Integer onlineStoreStatus;

    @ApiModelProperty(value = "仓库类型0-不是仓库，1-普通仓库，2-门店仓库，3-供货商仓库")
    private List<Integer> dcTypeList;

    @ApiModelProperty(value = "机构类型：同sys_organization表or_class一致)机构分类:1-集团；2-分子公司；3-仓库；4-数据中心；5-部门；6-加盟店；7-直营店 8-托管店")
    private Integer stClass;

    @ApiModelProperty(value = "所属分公司编码")
    private Collection<String> branchCodes;

    @ApiModelProperty(value = "所属分公司仓库编码")
    private Collection<String> warehouseCodes;

}
