package com.yxt.lotprice.service.adjustbase.model.bo;

import com.yxt.lotprice.service.model.enums.AdjustFlowType;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 调价审批人/报批人角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
public class PriceAdjustRoleConfigBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 配置编码
     */
    private String code;
    /**
     * 报批人类别名称
     */
    private String roleName;

    /**
     * 报批人类别编码
     */
    private String roleCode;

    /**
     * 项目属性：COMPANY-子公司，GROUP-集团
     */
    private String scopeName;

    /**
     * 所属子公司/集团编码
     */
    private String companyCode;

    /**
     * 定价/调价维度，STORE-门店,PRICE_GROUP-价格组,COMPANY-子公司
     */
    private String dimension;

    /**
     * 定价/调价类型，ADJUST-调价，PRICING-定价
     */
    private String priceOpsType;

    /**
     * 价格类型：RETAIL-零售价,VIP-会员价,CHRONIC-慢病价
     */
    private String priceType;

    /**
     * 角色类型-报批人/审批人
     */
    private String type;

    /**
     * 优先级,值越大，级别越高
     */
    private Integer priority;

    /**
     * 流程类型：长流程，短流程，不区分长短流程,多个用逗号隔开
     */
    private String flowType;


    public List<AdjustFlowType> flowTypeList() {
        return Arrays
                .stream(flowType.split(","))
                .filter(StringUtils::isNotBlank)
                .map(AdjustFlowType::valueOf)
                .collect(Collectors.toList());
    }

}
