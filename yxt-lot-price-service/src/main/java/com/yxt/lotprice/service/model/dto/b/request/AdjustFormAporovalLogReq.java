package com.yxt.lotprice.service.model.dto.b.request;

import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/29 10:08
 */
@Data
@ApiModel("调价单审批记录查询参数")
public class AdjustFormAporovalLogReq {
    @ApiModelProperty("调价单编号")
    private String code;

    @ApiModelProperty("机构编码")
    private String orCode;

    @ApiModelProperty("erp编码")
    private String erpCode;

    @ApiModelProperty("价格类型")
    private PriceTypeEnum  priceType;
}
