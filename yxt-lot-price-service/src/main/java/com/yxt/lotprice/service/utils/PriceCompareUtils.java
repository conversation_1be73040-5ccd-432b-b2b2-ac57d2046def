package com.yxt.lotprice.service.utils;

import com.yxt.lotprice.service.model.bo.PriceResultsBO;
import com.yxt.lotprice.service.model.bo.PriceTimelineSegmentBO;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * Since: 2025/05/26 16:14
 * Author: qs
 */
public class PriceCompareUtils {

    /**
     * 近期两段价格是否一致
     */
    public static boolean priceEqual(PriceResultsBO a, PriceResultsBO b) {
        return Objects.equals(a.getPrice(), b.getPrice()) &&
                Objects.equals(a.getStartTime(), b.getStartTime()) &&
                Objects.equals(a.getEndTime(), b.getEndTime()) &&
                Objects.equals(a.getNextPrice(), b.getNextPrice()) &&
                Objects.equals(a.getNextStartTime(), b.getNextStartTime()) &&
                Objects.equals(a.getNextEndTime(), b.getEndTime());
    }

    /**
     * 计算出的价格代是否一致
     */
    public static boolean arePriceTimelineListsEqual(List<PriceTimelineSegmentBO> list1, List<PriceTimelineSegmentBO> list2) {
        if (list1 == null || list2 == null) return Objects.equals(list1, list2);
        if (list1.size() != list2.size()) return false;

        for (int i = 0; i < list1.size(); i++) {
            if (!areSegmentsEqual(list1.get(i), list2.get(i))) {
                return false;
            }
        }

        return true;
    }

    private static boolean areSegmentsEqual(PriceTimelineSegmentBO a, PriceTimelineSegmentBO b) {
        return Objects.equals(a.getStart(), b.getStart()) &&
                Objects.equals(a.getEnd(), b.getEnd()) &&
                Objects.equals(a.getPrice(), b.getPrice()) &&
                Objects.equals(a.getFormNo(), b.getFormNo()) &&
                Objects.equals(a.getFormItemNo(), b.getFormItemNo()) &&
                Objects.equals(a.getOrgType(), b.getOrgType()) &&
                Objects.equals(a.getAllFormItemNos(), b.getAllFormItemNos()) &&
                areMergeListsEqual(a.getMergeList(), b.getMergeList());
    }

    private static boolean areMergeListsEqual(List<PriceTimelineSegmentBO.PriceTimeSegment> list1,
                                              List<PriceTimelineSegmentBO.PriceTimeSegment> list2) {
        if (list1 == null || list2 == null) return Objects.equals(list1, list2);
        if (list1.size() != list2.size()) return false;

        for (int i = 0; i < list1.size(); i++) {
            if (!areMergeItemsEqual(list1.get(i), list2.get(i))) {
                return false;
            }
        }

        return true;
    }

    private static boolean areMergeItemsEqual(PriceTimelineSegmentBO.PriceTimeSegment a,
                                              PriceTimelineSegmentBO.PriceTimeSegment b) {
        return Objects.equals(a.getStart(), b.getStart()) &&
                Objects.equals(a.getEnd(), b.getEnd()) &&
                Objects.equals(a.getPrice(), b.getPrice()) &&
                Objects.equals(a.getFormNo(), b.getFormNo()) &&
                Objects.equals(a.getFormItemNo(), b.getFormItemNo()) &&
                Objects.equals(a.getOrgType(), b.getOrgType()) &&
                Objects.equals(a.getAllFormItemNos(), b.getAllFormItemNos());
    }
}
