package com.yxt.lotprice.service.model.enums;

import lombok.Getter;

@Getter
public enum AdjustFlowType {
    LONG("长流程"),
    SHORT("短流程"),
    NONE("不区分长短流程");


    AdjustFlowType(String name) {
        this.nameCh = name;
    }
    private String nameCh;

    public static AdjustFlowType getByName(String name) {
        try {
            return AdjustFlowType.valueOf(name);
        }catch (Exception e){
        }
        return null;
    }
}
