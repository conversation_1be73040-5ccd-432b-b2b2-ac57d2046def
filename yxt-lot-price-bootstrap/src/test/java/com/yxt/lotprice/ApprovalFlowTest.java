package com.yxt.lotprice;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yxt.lotprice.service.ApprovalFlowService;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.ApprovalFlowPageReq;
import com.yxt.lotprice.service.model.enums.AdjustScopeType;
import com.yxt.lotprice.service.model.enums.AdjustType;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ApprovalFlowTest extends BaseTest{

    @Autowired
    ApprovalFlowService approvalFlowService;

    @Test
    public void testPage() {
        ApprovalFlowPageReq req = new ApprovalFlowPageReq();
        req.setCurrentPage(1l);
        req.setPageSize(10l);
        req.setName("测试");
        req.setAdjustType(Lists.newArrayList(AdjustType.PRICING));
        req.setScope(AdjustScopeType.COMPANY);
        System.out.println(JSON.toJSONString(req));
    }

    public static void main(String[] args) {
        new ApprovalFlowTest().testPage();
    }
}
