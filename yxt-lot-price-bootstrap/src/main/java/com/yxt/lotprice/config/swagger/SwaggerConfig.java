package com.yxt.lotprice.config.swagger;


import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
@EnableSwagger2
public class SwaggerConfig {

    @Bean
    public Docket cApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("接口-C端接口")  // 分组名称
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.yxt.lotprice.application.c"))  // 扫描的包路径
                .paths(PathSelectors.any())
                .build();
    }

    @Bean
    public Docket bApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("接口-B端接口")  // 分组名称
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.yxt.lotprice.application.b"))  // 扫描的包路径
                .paths(PathSelectors.any())
                .build();
    }

    @Bean
    public Docket apiApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("接口-feign接口")  // 分组名称
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.yxt.lotprice.application.api"))  // 扫描的包路径
                .paths(PathSelectors.any())
                .build();
    }

    @Bean
    public Docket thirdApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("接口-三方接口(http直接调用)")  // 分组名称
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.yxt.lotprice.application.third"))  // 扫描的包路径
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("API 文档")
                .description("Spring Boot 项目 API 文档")
                .version("1.0")
                .build();
    }
}
