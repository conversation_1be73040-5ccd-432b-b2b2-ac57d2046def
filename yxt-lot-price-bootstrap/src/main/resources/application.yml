server:
  port: 8080
api:
  version: 1.0

spring:
  profiles:
    active: local
  application:
    name: yxt-lot-price
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      discovery:
        server-addr: http://10.4.3.210:8848;
        namespace: fca9f3f3-c2a7-49ad-a3ea-d5fe988ceb30
        metadata:
          department: NR
        enabled: false
        register-enabled: false

  #db连接池配置规范（明确每一个参数的含义）：https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=15601434
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  shardingsphere:
    datasource:
      common:
        web-stat-filter:
          enabled: true
          url-pattern: /${api.version}/*
        stat-view-servlet:
          enabled: true
          url-pattern: /druid/*
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1 FROM DUAL
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ********************************************************************************************************************************************************************************
        username: agent
        password: FgRdxNn8ADFC
        initial-size: 4 # 初始化时建立物理连接的个数
        min-idle: 1   # 最小连接池数量
        max-active: 40 #  最大连接数量
        query-timeout: 60000 #查询超时
        filters: stat,config # stat表示sql合并
        testOnBorrow: false  # 申请连接时执行validationQuery检测连接是否有效
        socket-timeout: 60000
        keep-alive: true
      names: master,slave
      master:
        type: com.alibaba.druid.pool.DruidDataSource
        url: ********************************************************************************************************************************************************************************
        username: agent
        password: FgRdxNn8ADFC
      slave:
        type: com.alibaba.druid.pool.DruidDataSource
        url: ********************************************************************************************************************************************************************************
        username: agent
        password: FgRdxNn8ADFC
    rules:
      readwrite-splitting:
        load-balancers:
          round_robin:
            type: ROUND_ROBIN
        data-sources:
          read_write_db:
            type: Static
            props:
              write-data-source-name: master
              read-data-source-names: slave
            load-balancer-name: round_robin
      sharding:
        sharding-algorithms:
          company-store-algorithm:
            type: CLASS_BASED
            props:
              strategy: COMPLEX
              algorithmClassName: com.yxt.lotprice.infrastructure.config.sharding.CompanyStoreShardingAlgorithm
              virtual_num: 1024
              physical_num: 16
              bulk_threshold: 2
          adjust-form-item-algorithm:
            type: CLASS_BASED
            props:
              strategy: COMPLEX
              algorithmClassName: com.yxt.lotprice.infrastructure.config.sharding.AdjustFormItemShardingAlgorithm
              virtual_num: 1024
              physical_num: 16
              bulk_threshold: 2

        tables:
          price_adjust_form_item_detail:
            actual-data-nodes: read_write_db.price_adjust_form_item_detail_$->{['1000','2000']}_$->{0..15}
            table-strategy:
              complex:
                sharding-columns: company_code, store_code
                sharding-algorithm-name: company-store-algorithm  #引用分片算法 name: company-store-algorithm
          price_adjust_results:
            actual-data-nodes: read_write_db.price_adjust_results_$->{['1000','2000']}_$->{0..15}
            table-strategy:
              complex:
                sharding-columns: company_code, store_code
                sharding-algorithm-name: company-store-algorithm  #引用分片算法 name: company-store-algorithm
          price_adjust_form_item:
            actual-data-nodes: read_write_db.price_adjust_form_item_1_$->{0..15}
            table-strategy:
              complex:
                sharding-columns: parent_code
                sharding-algorithm-name: adjust-form-item-algorithm  #引用分片算法 name: company-store-algorithm

    props:
      sql-show: true

  jackson:
    time-zone: Asia/Shanghai
  redis:
    password: yxt_redis123
    jedis:
      pool:
        min-idle: 10
        max-active: 200
        max-idle: 50
        max-wait: 1000
    timeout: 1000
    cluster:
      nodes: **********:9000,**********:9001,**********:9002,**********:9003,**********:9004,**********:9005
      max-redirects: 3
#  elasticsearch:
#    rest:
#      uris: ["http://**********:9200"]
##      username: 【username】
##      password: 【password】
#    max-connect-num: 200
#    max-connect-per-route: 50
#    concurrent-requests: 1
#    bulk-actions: 200
#    flush_interval: 60
#  data:
#    mongodb:
##     uri: mongodb://【username】:【password】@【ip1】:【port1】,【ip2】:【port2】,【ipN】:【portN】/【dbName】
#      uri: *****************************************************************
#  kafka:
#    #Kafka集群
#    bootstrap-servers: **********:9092
#    consumer:
#      # 当kafka中没有初始offset或offset超出范围时将自动重置offset
#      # earliest:重置为分区中最小的offset;
#      # latest:重置为分区中最新的offset(消费分区中新产生的数据);
#      # none:只要有一个分区不存在已提交的offset,就抛出异常;
#      auto-offset-reset: latest
#      #自动提交offset
#      enable-auto-commit: true
#      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      group-id: ${spring.application.name}-${spring.profiles.active}


mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.yxt.lotprice.infrastructure.dao.po
  configuration:
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      update-strategy: not_null
      id-type: auto

grey:
  enable: true
  local-mappings:
    '[(.+)]': $1.svc.k8s.dev.hxyxt.com
management:
  endpoint:
    mappings:
      enabled: true
    httptrace:
      enabled: true
  endpoints:
    web:
      exposure:
        include: [ "*" ]
swagger:
  enable: true

logging:
  level:
    root: INFO
    org.apache.http: INFO
    org.apache.zookeeper: ERROR
    com.ctrip.framework.apollo: ERROR
MaxHistory: 7

rocketmq:
  name-server: **********:9876
  #生产者
  producer:
    group: PGROUP_${spring.application.name}_${spring.profiles.active}
    customized-trace-topic: TOPIC_MIDDLE_MEMBER_CHRONIC_DEV
    send-message-timeout: 300000
    compress-message-body-threshold: 4096
    max-message-size: 4194304
    retry-times-when-send-failed: 2
    retry-times-when-send-async-failed: 0
    retry-next-server: true

### Ribbon 配置
ribbon:
  # 连接超时
  ConnectTimeout: 3000
  # 响应超时
  ReadTimeout: 5000
feign:
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000
        loggerLevel: full
      yxt-crm:
        connectTimeout: 5000
        readTimeout: 5000
        loggerLevel: NONE

session:
  timeout:
    minutes: 86400
token:
  timeout:
    minutes: 604800
alarm:
  robot:
    # 是否开启机器人告警，默认开启；非必填
    enable: true
    # 值班人手机号，英文逗号分隔；非必填
    oncallMobile:

#xxl:
#  job:
#    admin:
#      addresses: http://xxl-job-admin:8080/xxl-job-admin/
#    executor:
#      appname:
#      port:
#      ip:
#      address:
#      logpath: /data/logs/applogs/xxl-job/jobhandler
#      logretentiondays: 5
#    accessToken: sk_token

