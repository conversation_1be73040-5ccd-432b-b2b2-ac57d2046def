package com.yxt.lotprice.application.b;


import com.yxt.lang.constants.response.ResponseCodeType;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lotprice.application.LotPriceAbstractController;
import com.yxt.lotprice.service.ApprovalFlowService;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.AdjustRoleReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.ApprovalFlowPageReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.ApprovalFlowSaveDetailReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.ApprovalFlowSwitchReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase.AdjustRoleItemResp;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase.ApprovalFlowPageResp;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase.ApprovalFlowQueryDetailResp;
import com.yxt.lotprice.service.model.enums.AdjustStatusType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/b/approval-flow")
@Api(tags = "调价审批流")
@Slf4j
public class ApprovalFlowController extends LotPriceAbstractController {

    @Autowired
    private ApprovalFlowService approvalFlowService;
    @ApiOperation(value = "分页查询审批流")
    @PostMapping(value = "/page")
    ResponseBase<PageDTO<ApprovalFlowPageResp>> page(@RequestBody @Valid ApprovalFlowPageReq req){
        return ResponseBase.success(approvalFlowService.page(req));
    }

    @ApiOperation(value = "新增/修改审批流")
    @PostMapping(value = "/save")
    ResponseBase<String> save(@RequestBody @Valid ApprovalFlowSaveDetailReq req){

        List<ApprovalFlowPageResp> data = approvalFlowService.existApprovalFlow(req.getScope(), req.getFlowType(), req.getCompanyCode(), req.getAdjustType());
        //查询如果存在了，就报错审批流已存在
        if (CollectionUtils.isNotEmpty(data)) {
            return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION.getCode(), "已有有效审批流，请先停用！有效审批流名称:"+data.stream().map(ApprovalFlowPageResp::getName).collect(Collectors.toList()));
        }
        Long count = approvalFlowService.save(req, getUserName());
        return ResponseBase.success(count+"");
    }

    @ApiOperation(value = "查询审批流详情")
    @PostMapping(value = "/detail/{id}")
    ResponseBase<ApprovalFlowQueryDetailResp> detail(@RequestBody @PathVariable("id") Long id){
        return ResponseBase.success(approvalFlowService.detail(id));
    }

    @ApiOperation(value = "查询审批人角色接口")
    @PostMapping(value = "role/list")
    ResponseBase<List<AdjustRoleItemResp>> roleList(@RequestBody AdjustRoleReq req){
        return ResponseBase.success(approvalFlowService.roleList(req));
    }


    @ApiOperation(value = "审批流停用/启用开关")
    @PostMapping(value = "switch")
    ResponseBase<Boolean> switchs(@RequestBody ApprovalFlowSwitchReq req){
        if (req.getStatus() == AdjustStatusType.OFF){
            return ResponseBase.success(approvalFlowService.statusSwitch(req, getUserName()));
        }
        ApprovalFlowQueryDetailResp detail = approvalFlowService.detail(req.getId());
        if (detail == null || detail.getId() == null){
            return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION.getCode(),"当前审批流不存在，更新失败！id:"+req.getId());
        }

        if(detail.getStatus() == req.getStatus()){
            return ResponseBase.success(true);
        }

        List<ApprovalFlowPageResp> data = approvalFlowService.existApprovalFlow(detail.getScope(), detail.getFlowType(), detail.getCompanyCode(), detail.getAdjustType());
        List<ApprovalFlowPageResp> existData = data.stream().filter(item -> !item.getId().equals(req.getId())).collect(Collectors.toList());
        if(!existData.isEmpty()){
            return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION.getCode(), "已有有效审批流，请先停用！有效审批流名称/编码:"+existData.stream().map(x->x.getName()+"/"+x.getCode()).collect(Collectors.toList()));
        }else {
            return ResponseBase.success(approvalFlowService.statusSwitch(req, getUserName()));
        }

    }

}
