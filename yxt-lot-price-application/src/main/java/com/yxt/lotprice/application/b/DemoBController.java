package com.yxt.lotprice.application.b;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lotprice.service.DemoChoincStoreService;
import com.yxt.lotprice.service.model.dto.b.request.DemoBReq;
import com.yxt.lotprice.service.model.dto.b.response.DemoBResp;
import com.yxt.lotprice.service.DemoService;
import com.yxt.lotprice.service.model.bo.DemoBO;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@RestController
@RequestMapping(value = "/b/demo")
@Api(tags = "B端接口Demo")
@Slf4j
public class DemoBController extends AbstractController {

    @Resource
    private DemoChoincStoreService service;


    @ApiOperation(value = "B端demo-查询员工数大于指定数值的门店列表", notes = "B端demo-查询员工数大于指定数值的门店")
    @PostMapping(value = "/list")
    ResponseBase<List<DemoBResp>> getByIdFromMysql(@RequestBody DemoBReq demoReq){
        List<DemoBResp> list = service.list(demoReq);
        return ResponseBase.success(list);
    }
}
