package com.yxt.lotprice.application.b;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.ApprovalFlowSwitchReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.OrgTreeReq;
import com.yxt.lotprice.service.cache.BaseInfoCacheService;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/b/price-adjust")
@Api(tags = "公共controller")
@Slf4j
public class CommonController {

    @Autowired
    BaseInfoCacheService baseInfoCacheService;


    @ApiOperation(value = "查询组织机构树，当前节点及其子节点")
    @PostMapping(value = "orgTree")
    ResponseBase<SysOrganizationDTO> orgTree(@RequestBody OrgTreeReq req){
        if (StringUtils.isBlank(req.getOrgCode())){
            req.setOrgCode("yxt");
        }
        SysOrganizationDTO sysOrganizationDTO = baseInfoCacheService.getOrgList().stream().filter(item -> req.getOrgCode().equals(item.getOrCode())).findFirst().orElse(null);
        return ResponseBase.success(sysOrganizationDTO);
    }
}
