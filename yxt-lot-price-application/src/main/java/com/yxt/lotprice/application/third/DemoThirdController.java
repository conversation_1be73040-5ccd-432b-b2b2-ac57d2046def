package com.yxt.lotprice.application.third;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lotprice.service.DemoChoincStoreService;
import com.yxt.lotprice.service.model.bo.DemoBO;
import com.yxt.lotprice.service.model.dto.third.request.DemoThirdReq;
import com.yxt.lotprice.service.model.dto.third.response.DemoThirdResp;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping(value = "/third/demo")
@Api(tags = "三方接口Demo")
@Slf4j
public class DemoThirdController extends AbstractController {

    @Resource
    private DemoChoincStoreService service;

    @ApiOperation(value = "三方demo-分页查询慢病会员数大于指定值的门店信息", notes = "三方demo-分页查询慢病会员数大于指定值的门店信息")
    @PostMapping(value = "/page")
    ResponseBase<PageDTO<DemoThirdResp>> page(@RequestBody DemoThirdReq demoReq){
        PageDTO<DemoThirdResp> page = service.page(demoReq);
        return generateSuccess(page);
    }
}
