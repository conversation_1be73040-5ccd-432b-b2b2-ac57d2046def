package com.yxt.lotprice.application.mq.rocketmq.consumer;

import com.yxt.lotprice.service.calculation.PriceAdjustCalculationService;
import com.yxt.lotprice.service.model.dto.mq.rocketmq.AdjustPriceFormItemDTO;
import com.yxt.lotprice.service.model.enums.log.PriceCalculateSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static org.apache.rocketmq.spring.annotation.ConsumeMode.ORDERLY;

/**
 * 调价单项目消息消费者
 * 
 * <AUTHOR>
 * @since 2025-05-21
 */
@Slf4j
@Component
@RocketMQMessageListener(
    topic = "ADJUST_PRICE_FORM_ITEM", 
    consumerGroup = "ADJUST_PRICE_FORM_ITEM_${spring.profiles.active}", consumeMode = ORDERLY)
public class AdjustPriceFormItemConsumer implements RocketMQListener<AdjustPriceFormItemDTO> {

    @Resource
    private PriceAdjustCalculationService priceAdjustCalculationService;

    @Override
    public void onMessage(AdjustPriceFormItemDTO message) {
        try {
            log.info("接收到调价单项目消息: {}", message);
            
            if (message == null || message.getId() == null) {
                log.error("调价单项目消息无效: {}", message);
                return;
            }
            
            // 处理调价单项目
            priceAdjustCalculationService.processAdjustPriceFormItem(message.getId(), PriceCalculateSourceEnum.APPROVAL);
        } catch (Exception e) {
            log.error("处理调价单项目消息异常, 消息: {}, 异常: {}", message, e.getMessage(), e);
            throw e;
        }
    }
}
