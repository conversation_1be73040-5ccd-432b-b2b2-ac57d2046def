package com.yxt.lotprice.application.c;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lotprice.service.DemoService;
import com.yxt.lotprice.service.model.bo.DemoBO;
import com.yxt.lotprice.service.model.dto.c.request.DemoCReq;
import com.yxt.lotprice.service.model.dto.c.response.DemoCResp;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping(value = "/c/demo")
@Api(tags = "C端接口Demo")
@Slf4j
public class DemoCController extends AbstractController {

    @Resource
    private DemoService service;

    @ApiOperation(value = "c端demo-通过词典ID查询慢病词典信息", notes = "c端demo-通过词典ID查询慢病词典信息")
    @PostMapping(value = "/getById")
    ResponseBase<DemoCResp> getByIdFromMysql(@RequestBody DemoCReq demoReq){
        DemoCResp demoCResp = service.selectDictByIdForC(demoReq.getDiseaseDictId());
        return generateSuccess(demoCResp);
    }
}
