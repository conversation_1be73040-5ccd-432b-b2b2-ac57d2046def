package com.yxt.lotprice.application.api.impl;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lotprice.application.api.convert.DemoAppEntityConvert;
import com.yxt.lotprice.common.model.dto.request.DemoReq;
import com.yxt.lotprice.common.model.dto.response.DemoResp;
import com.yxt.lotprice.sdk.xxx.api.DemoApi;
import com.yxt.lotprice.service.DemoService;
import com.yxt.lotprice.service.model.bo.DemoBO;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Api(tags = "feign接口Demo")
@Slf4j
public class DemoApiImpl extends AbstractController implements DemoApi {
    @Resource
    private DemoService service;
    @Autowired
    private DemoAppEntityConvert demoAppEntitytConvert;

    @Override
    public ResponseBase<DemoResp> getByIdFromRedis(DemoReq demoReq) {
        DemoBO demoBO = service.selectDictByIdFromRedis(demoReq.getDiseaseDictId());
        return generateSuccess(demoAppEntitytConvert.toDictResp(demoBO));
    }
}
