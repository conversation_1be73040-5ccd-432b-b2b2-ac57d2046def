package com.yxt.lotprice.application.task.handler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CommodityVolumeTaskHandler {

    @XxlJob("commodityVolumeJob")
    public void execute() throws Exception {
        String param = XxlJobHelper.getJobParam();
        // todo 调用service/manager逻辑
        XxlJobHelper.handleSuccess();
    }
}
