package com.yxt.lotprice.application.mq.rocketmq.consumer;

import cn.hutool.core.collection.CollUtil;
import com.yxt.lotprice.service.model.dto.mq.rocketmq.ErpSyncAllOrgPriceDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;


@Slf4j
@Component
@RocketMQMessageListener(topic = "ALL_ORG_PRICE_SYNC_TOPIC", consumerGroup = "ALL_ORG_PRICE_SYNC_TOPIC_${spring.profiles.active}")
public class AllOrgPriceSyncConsumer implements RocketMQListener<ErpSyncAllOrgPriceDTO> {

    @Override
    public void onMessage(ErpSyncAllOrgPriceDTO dto) {
        try {
            if (CollUtil.isEmpty(dto.getPriceInfos())) {
                log.info("AllOrgPriceSyncListener#接收到同步全量价格#size=0");
                return;
            }
            log.info("AllOrgPriceSyncListener#transferTime={},size={}", dto.getTransferTime(), dto.getPriceInfos().size());
            //todo 调用service/manager逻辑
        } catch (Exception e) {
            log.info("AllOrgPriceSyncListener#e={}", e.getMessage());
        }
    }
}
