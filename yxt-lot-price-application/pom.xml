<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-lot-price-application</artifactId>
    <name>yxt-lot-price-application</name>
    <description>yxt-lot-price-application</description>

    <parent>
        <groupId>com.yxt.lotprice</groupId>
        <artifactId>yxt-lot-price</artifactId>
        <version>${reversion}</version>
    </parent>

    <properties>
        <module.deploy.skip>true</module.deploy.skip>
    </properties>

    <dependencies>

        <!--lotprice项目相关依赖-->
        <dependency>
            <groupId>com.yxt.lotprice</groupId>
            <artifactId>yxt-lot-price-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt.lotprice</groupId>
            <artifactId>yxt-lot-price-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt.lotprice</groupId>
            <artifactId>yxt-lot-price-manager</artifactId>
        </dependency>

        <!--         xxljob 依赖-->
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-xxljob-spring-boot-starter</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>org.springframework.cloud</groupId>-->
        <!--            <artifactId>spring-cloud-starter-openfeign</artifactId>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.squareup.okhttp3</groupId>-->
        <!--            <artifactId>okhttp</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
