<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-lot-price-sdk</artifactId>
    <name>yxt-lot-price-sdk</name>
    <description>yxt-lot-price-sdk</description>

    <parent>
        <groupId>com.yxt.lotprice</groupId>
        <artifactId>yxt-lot-price</artifactId>
        <version>${reversion}</version>

    </parent>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <module.deploy.skip>false</module.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yxt.lotprice</groupId>
            <artifactId>yxt-lot-price-common-model</artifactId>
        </dependency>


        <!-- springcloud openfeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- common-lang -->
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-lang</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

</project>
