<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-lot-price-infrastructure</artifactId>
    <name>yxt-lot-price-infrastructure</name>
    <description>yxt-lot-price-infrastructure</description>

    <parent>
        <groupId>com.yxt.lotprice</groupId>
        <artifactId>yxt-lot-price</artifactId>
        <version>${reversion}</version>
    </parent>

    <properties>
        <module.deploy.skip>true</module.deploy.skip>
    </properties>

    <dependencies>
        <!-- orm相关依赖 -->
        <!-- 引入shardingJDBC后，需增加类型转换，顺序要在mybatis之前-->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-typehandlers-jsr310</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <!--        todo starter 有问题，加密工具报错，starter里面的Controller注入失败 -->
<!--        <dependency>-->
<!--            <groupId>com.yxt</groupId>-->
<!--            <artifactId>yxt-mybatisplus-spring-boot-starter</artifactId>-->
<!--        </dependency>-->
        <!--数据源相关依赖-->
<!--        <dependency>-->
<!--            <groupId>org.apache.shardingsphere</groupId>-->
<!--            <artifactId>sharding-jdbc-spring-boot-starter</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>
            <version>5.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>

        <!--数据库相关依赖-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>



        <!-- mongodb组件相关依赖 -->
        <!--
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-mongodb</artifactId>
                </dependency>
        -->



        <!-- lotprice组件相关依赖 -->
        <dependency>
            <groupId>com.yxt.lotprice</groupId>
            <artifactId>yxt-lot-price-common-lib</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--  redis相关依赖 -->
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-redis-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.yxt</groupId>
                <artifactId>yxt-mvc-dalgenplugin</artifactId>
                <version>1.0.6</version>
                <configuration>
                    <baseDir>${basedir}</baseDir>
                    <projectName>${artifactId}</projectName>
                    <dataSource>
                        <driverName>com.mysql.jdbc.Driver</driverName>
                        <url>***************************************************</url>
                        <username>agent</username>
                        <password>FgRdxNn8ADFC</password>
                    </dataSource>
                    <strategy>
                        <!--
                          表生成策略：
                            1. 如果填写 property，则只生成填写的表
                            2. 如果不填写 property，则生成所有表
                          建议每次填写需要生成的表，避免生成不必要的表，造成以外
                        -->
                        <include>
                            <property>table_name</property>
                        </include>
                    </strategy>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>8.0.11</version>
                    </dependency>
                    <dependency>
                        <groupId>org.apache.velocity</groupId>
                        <artifactId>velocity</artifactId>
                        <version>1.7</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
</project>
