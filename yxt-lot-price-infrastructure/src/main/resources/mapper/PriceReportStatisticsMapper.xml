<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceReportStatisticsMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into price_report_statistics (
        id,
        erp_code,
        company_code,
        price_type,
        price,
        store_count,
        store_info,
        created_time,
        updated_time
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.erpCode},
        #{item.companyCode},
        #{item.priceType},
        #{item.price},
        #{item.storeCount},
        #{item.storeInfo},
        #{item.createdTime},
        #{item.updatedTime}
        )
        </foreach>
    </insert>
</mapper>
