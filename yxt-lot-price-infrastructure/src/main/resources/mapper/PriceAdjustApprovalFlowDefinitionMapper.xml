<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustApprovalFlowDefinitionMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into price_adjust_approval_flow_definition (
        id,
        code,
        name,
        scope,
        company_code,
        dimension,
        flow_type,
        status,
        node_config,
        create_time,
        create_name,
        modify_time,
        modify_name
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.code},
        #{item.name},
        #{item.scope},
        #{item.companyCode},
        #{item.dimension},
        #{item.flowType},
        #{item.status},
        #{item.nodeConfig},
        #{item.createTime},
        #{item.createName},
        #{item.modifyTime},
        #{item.modifyName}
        )
        </foreach>
    </insert>
</mapper>
