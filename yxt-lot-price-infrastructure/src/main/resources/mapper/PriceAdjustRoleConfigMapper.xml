<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustRoleConfigMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into price_adjust_role_config (
        id,
        name,
        code,
        scope_name,
        scope,
        dimension,
        price_ops_type,
        price_type,
        type,
        priority
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.name},
        #{item.code},
        #{item.scopeName},
        #{item.scope},
        #{item.dimension},
        #{item.priceOpsType},
        #{item.priceType},
        #{item.type},
        #{item.priority}
        )
        </foreach>
    </insert>
</mapper>
