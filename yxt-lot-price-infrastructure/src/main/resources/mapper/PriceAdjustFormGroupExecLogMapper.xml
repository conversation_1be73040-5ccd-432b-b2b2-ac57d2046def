<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustFormGroupExecLogMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into price_adjust_form_group_exec_log (
        id,
        form_id,
        create_time,
        create_name,
        modify_name,
        modify_time,
        desc,
        exec,
        org_code
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.formId},
        #{item.createTime},
        #{item.createName},
        #{item.modifyName},
        #{item.modifyTime},
        #{item.desc},
        #{item.exec},
        #{item.orgCode}
        )
        </foreach>
    </insert>
</mapper>
