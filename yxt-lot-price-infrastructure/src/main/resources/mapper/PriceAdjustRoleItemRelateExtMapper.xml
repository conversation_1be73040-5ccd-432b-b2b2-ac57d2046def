<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustRoleItemRelateExtMapper">
    <resultMap id="BaseResultMap" type="com.yxt.lotprice.infrastructure.dao.po.PriceAdjustRoleItemRelateExtPO">
        <id column="id" jdbcType="INTEGER" property="id" />
<!--        <result column="storeCode" jdbcType="VARCHAR" property="storeCode" />-->
    </resultMap>

    <insert id="insertBatch">

        insert into price_adjust_role_item_relate_ext (
        item_relate_id,
        or_code,
        user_id,
        role_type
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
            (
            #{item.itemRelateId},
            #{item.orCode},
            #{item.userId},
            #{item.roleType}
            )
        </foreach>
    </insert>
</mapper>
