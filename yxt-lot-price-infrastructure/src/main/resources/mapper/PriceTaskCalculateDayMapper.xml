<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceTaskCalculateDayMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into price_task_calculate_day (
        id,
        status,
        execute_date,
        data,
        created_time,
        updated_time
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.status},
        #{item.executeDate},
        #{item.data},
        #{item.createdTime},
        #{item.updatedTime}
        )
        </foreach>
    </insert>
</mapper>
