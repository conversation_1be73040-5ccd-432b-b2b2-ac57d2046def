<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustRoleRelateMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into price_adjust_role_relate (
        id,
        name,
        code,
        company_code,
        role_code,
        type,
        desc,
        create_time,
        create_name,
        modify_time,
        modify_name
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.name},
        #{item.code},
        #{item.companyCode},
        #{item.roleCode},
        #{item.type},
        #{item.desc},
        #{item.createTime},
        #{item.createName},
        #{item.modifyTime},
        #{item.modifyName}
        )
        </foreach>
    </insert>
</mapper>
