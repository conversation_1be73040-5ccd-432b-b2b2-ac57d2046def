<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustAporovalTaskMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into price_adjust_aporoval_task (
        id,
        price_adjust_form_id,
        status,
        aporoval_progress,
        dimension,
        cancel_status,
        create_time,
        create_name,
        modify_name,
        modify_time,
        create_user_id,
        desc,
        price_ops_type,
        flow_definition_id
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.priceAdjustFormId},
        #{item.status},
        #{item.aporovalProgress},
        #{item.dimension},
        #{item.cancelStatus},
        #{item.createTime},
        #{item.createName},
        #{item.modifyName},
        #{item.modifyTime},
        #{item.createUserId},
        #{item.desc},
        #{item.priceOpsType},
        #{item.flowDefinitionId}
        )
        </foreach>
    </insert>
</mapper>
