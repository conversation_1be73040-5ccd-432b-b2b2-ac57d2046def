<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustFormAuthMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into price_adjust_form_auth (
        id,
        price_adjust_form_id,
        auth_data,
        create_user_id,
        create_name,
        create_time,
        modify_name,
        modify_time
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.priceAdjustFormId},
        #{item.authData},
        #{item.createUserId},
        #{item.createName},
        #{item.createTime},
        #{item.modifyName},
        #{item.modifyTime}
        )
        </foreach>
    </insert>
</mapper>
