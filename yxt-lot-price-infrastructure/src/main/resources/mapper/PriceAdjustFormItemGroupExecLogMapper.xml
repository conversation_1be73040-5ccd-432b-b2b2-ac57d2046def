<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustFormItemGroupExecLogMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into price_adjust_form_item_group_exec_log (
        id,
        form_item_id,
        stores,
        desc
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.formItemId},
        #{item.stores},
        #{item.desc}
        )
        </foreach>
    </insert>
</mapper>
