<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustFormMapper">


    <resultMap id="PriceAdjustFormResultMap" type="PriceAdjustFormPO">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="scope" property="scope" />
        <result column="dimension" property="dimension" />
        <result column="cancel_status" property="cancelStatus" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="remark" property="remark" />
        <result column="price_ops_type" property="priceOpsType" />
        <result column="flow_definition_id" property="flowDefinitionId" />
        <result column="flow_definition" property="flowDefinition" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="status" property="status" />
        <result column="approval_time" property="approvalTime" />
        <result column="create_user_role" property="createUserRole" />
    </resultMap>

    <sql id="Base_Column_List">
        paf.*
    </sql>

    <select id="pageJoinAuth" resultMap="PriceAdjustFormResultMap" parameterType="PriceAdjustFormAuthSearchPO">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        price_adjust_form paf
        JOIN
        price_adjust_form_auth pafa ON paf.id = pafa.price_adjust_form_id
        <where>
            1=1
            <if test="params.startTime != null">
                AND paf.create_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND paf.create_time &lt;= #{endTime}
            </if>
            <if test="params.createName != null and params.createName != ''">
                AND paf.create_name = #{params.createName}
            </if>
            <if test="params.code != null and params.code != ''">
                AND paf.code = #{params.code}
            </if>
            <if test="params.auditStatus != null and params.auditStatus.size() > 0">
                AND paf.status IN
                <foreach collection="params.auditStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="params.scope != null and params.scope.size() > 0">
                AND paf.scope IN
                <foreach collection="params.scope" item="s" open="(" separator="," close=")">
                    #{s}
                </foreach>
            </if>
            <if test="params.submiterRoleCode != null and params.submiterRoleCode.size() > 0">
                AND paf.create_user_role IN
                <foreach collection="params.submiterRoleCode" item="role" open="(" separator="," close=")">
                    #{role}
                </foreach>
            </if>
            <if test="params.dimensions != null and params.dimensions.size() > 0">
                AND paf.dimension IN
                <foreach collection="params.dimensions" item="dim" open="(" separator="," close=")">
                    #{dim}
                </foreach>
            </if>
            <if test="params.name != null and params.name != ''">
                AND paf.name = #{name}
            </if>
            <if test="params.cancelStatus != null and params.cancelStatus.size() > 0">
                AND paf.cancel_status IN
                <foreach collection="params.cancelStatus" item="cancel" open="(" separator="," close=")">
                    #{cancel}
                </foreach>
            </if>
            <if test="params.createUserId != null and params.createUserId != ''">
                AND paf.create_user_id = #{params.createUserId}
            </if>

            <!--auth data-->
            <if test="params.authData != null and params.authData.size() > 0">
                AND (
                <foreach collection="params.authData" item="auth" separator="OR">
                    (
                    <if test="auth.orgCode != null and auth.orgCode != ''">
                        pafa.org_code = #{auth.orgCode}
                    </if>
                    <if test="auth.roleCode != null and auth.roleCode != ''">
                        AND JSON_CONTAINS(flow_definition->'$.configNodeList[*].roleCode',JSON_QUOTE(#{auth.roleCode}))
                    </if>
                    <if test="auth.parentOrgIdPath != null and auth.parentOrgIdPath != ''">
                        AND pafa.org_id_path = #{auth.parentOrgIdPath}
                    </if>
                    <if test="auth.companyCode != null and auth.companyCode != ''">
                        AND pafa.company_code = #{auth.companyCode}
                    </if>
                    )
                </foreach>
                )
            </if>
        </where>
        ORDER BY paf.create_time DESC
    </select>


    <insert id="insertBatch">
        insert into price_adjust_form (
        id,
        code,
        name,
        scope,
        dimension,
        cancel_status,
        create_time,
        create_name,
        modify_name,
        modify_time,
        create_user_id,
        desc,
        price_ops_type,
        flow_definition_id,
        flow_definition,
        status,
        approval_time,
        create_user_role
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.code},
            #{item.name},
            #{item.scope},
            #{item.dimension},
            #{item.cancelStatus},
            #{item.createTime},
            #{item.createName},
            #{item.modifyName},
            #{item.modifyTime},
            #{item.createUserId},
            #{item.desc},
            #{item.priceOpsType},
            #{item.flowDefinitionId},
            #{item.flowDefinition},
            #{item.status},
            #{item.approvalTime},
            #{item.createUserRole}
            )
        </foreach>
    </insert>

</mapper>
