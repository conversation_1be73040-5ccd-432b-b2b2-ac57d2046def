<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustResultsMapper">



    <!--  追加价格变动日志  -->
    <sql id="appendPriceChangeLogWithLimit">
    <![CDATA[
        price_change_log = IF(
            JSON_LENGTH(price_change_log) < 50,
            JSON_ARRAY_APPEND(price_change_log, '$', CAST(#{item.priceChangeLog} AS JSON)),
        JSON_ARRAY_APPEND(JSON_REMOVE(price_change_log, '$[0]'), '$', CAST(#{item.priceChangeLog} AS JSON))
        )
        ]]>
    </sql>

    <!--  追加价格代变化日志  -->
    <sql id="appendPriceResultLogWithLimit">
    <![CDATA[
        price_result_log = IF(
            JSON_LENGTH(price_result_log) < 50,
            JSON_ARRAY_APPEND(price_result_log, '$', CAST(#{item.priceResultLog} AS JSON)),
        JSON_ARRAY_APPEND(JSON_REMOVE(price_result_log, '$[0]'), '$', CAST(#{item.priceResultLog} AS JSON))
        )
        ]]>
    </sql>

    <insert id="insertBatch">
        insert into price_adjust_results (
        erp_code,
        store_id,
        store_code,
        company_code,
        price_type,
        price,
        start_time,
        end_time,
        next_price,
        next_start_time,
        next_end_time,
        push_pos_status,
        price_result_log,
        price_change_log,
        push_pos_log
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
            (
            #{item.erp_code},
            #{item.store_id},
            #{item.store_code},
            #{item.company_code},
            #{item.price_type},
            #{item.price},
            #{item.start_time},
            #{item.end_time},
            #{item.next_price},
            #{item.next_start_time},
            #{item.next_end_time},
            #{item.push_pos_status},
            #{item.price_result_log},
            #{item.price_change_log},
            #{item.push_pos_log}
            )
        </foreach>
    </insert>

    <update id="updatePriceSegment">
        <foreach collection="records" item="item" separator=";">
            UPDATE price_adjust_results
            SET
            price = #{item.price},
            start_time = #{item.startTime},
            end_time = #{item.endTime},
            next_price = #{item.nextPrice},
            next_start_time = #{item.nextStartTime},
            next_end_time = #{item.nextEndTime},
            price_result_segment = #{item.priceResultSegment},
            push_pos_status = #{item.pushPosStatus},
            <include refid="appendPriceChangeLogWithLimit"/>,
            <include refid="appendPriceResultLogWithLimit"/>
            WHERE id = #{item.id}
            AND store_code = #{item.storeCode},
            AND company_code = #{item.companyCode},
        </foreach>
    </update>

    <update id="updatePriceSegmentOnly">
        <foreach collection="records" item="item" separator=";">
            UPDATE price_adjust_results
            SET
            price_result_segment = #{item.priceResultSegment},
            <include refid="appendPriceResultLogWithLimit"/>
            WHERE id = #{item.id}
            AND store_code = #{item.storeCode},
            AND company_code = #{item.companyCode},
        </foreach>
    </update>

    <update id="updatePrice">
        <foreach collection="records" item="item" separator=";">
            UPDATE price_adjust_results
            SET
            price = #{item.price},
            start_time = #{item.startTime},
            end_time = #{item.endTime},
            next_price = #{item.nextPrice},
            next_start_time = #{item.nextStartTime},
            next_end_time = #{item.nextEndTime},
            push_pos_status = #{item.pushPosStatus},
            <include refid="appendPriceChangeLogWithLimit"/>,
            WHERE id = #{item.id}
            AND store_code = #{item.storeCode},
            AND company_code = #{item.companyCode},
        </foreach>
    </update>

    <select id="selectByErpCodePriceTypeStoreCode"
            resultType="com.yxt.lotprice.infrastructure.dao.po.PriceAdjustResultsPO">
        SELECT
        id,
        erp_code,
        store_id,
        store_code,
        company_code,
        price_type,
        price,
        start_time,
        end_time,
        next_price,
        next_start_time,
        next_end_time,
        push_pos_status,
        created_time,
        updated_time
        FROM ${tableName}
        <where>
            erp_code = #{erpCode}
            , price_type = #{priceType}
            , store_code IN
            <foreach collection="storeCodes" item="storeCode" open="(" close=")" separator=",">
                #{storeCode}
            </foreach>
        </where>
    </select>
</mapper>
