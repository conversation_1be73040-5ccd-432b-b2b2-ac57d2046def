<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustRoleItemRelateMapper">


    <!--  -->

    <insert id="insertBatch">
        insert into price_adjust_role_item_relate (
        id,
        parent_code,
        company_code,
        role_code,
        role_data,
        role_type,
        create_time,
        create_name,
        modify_time,
        modify_name
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.parentCode},
            #{item.companyCode},
            #{item.roleCode},
            #{item.roleData},
            #{item.roleType},
            #{item.createTime},
            #{item.createName},
            #{item.modifyTime},
            #{item.modifyName}
            )
        </foreach>
    </insert>

    <resultMap id="PriceAdjustRoleItemRelateResultMap" type="PriceAdjustRoleItemRelateJoinExtPO">
        <result property="companyCode" column="company_code"/>
        <result property="approverRoleCode" column="approver_role_code"/>
        <result property="orgCode" column="org_code"/>
        <result property="userId" column="user_id"/>
        <result property="submiterRoleCode" column="submiter_role_code"/>
        <result property="roleType" column="role_type"/>
    </resultMap>

    <select id="selectApproverJoinExt" resultMap="PriceAdjustRoleItemRelateResultMap" parameterType="PriceAdjustRoleItemRelateSearchPO">
        SELECT
        r.company_code,
        r.role_code AS approver_role_code,
        r.role_type,
        ext.or_code AS org_code,
        ext.user_id,
        ext.role_type AS submiter_role_code
        FROM
        price_adjust_role_item_relate r
        INNER JOIN
        price_adjust_role_item_relate_ext ext ON r.id = ext.item_relate_id
        WHERE 1=1
        <if test="companyCode != null and companyCode != ''">
            AND r.company_code = #{companyCode}
        </if>
        <if test="roleCode != null and roleCode != ''">
            AND r.role_code = #{roleCode}
        </if>
        <if test="orgCode != null and orgCode != ''">
            AND ext.or_code = #{orgCode}
        </if>
        <if test="userId != null and userId != ''">
            AND ext.user_id = #{userId}
        </if>
            AND r.role_type = 'APPROVER'
    </select>
</mapper>
