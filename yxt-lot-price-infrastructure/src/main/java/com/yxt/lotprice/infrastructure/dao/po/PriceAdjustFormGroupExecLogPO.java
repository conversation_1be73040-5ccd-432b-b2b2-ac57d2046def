package com.yxt.lotprice.infrastructure.dao.po;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 集团指导价执行记录表-分公司级
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@TableName("price_adjust_form_group_exec_log")
public class PriceAdjustFormGroupExecLogPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 调价单id
     */
    private Long formId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 修改人
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 执行备注
     */
    private String desc;

    /**
     * 是否已执行
     */
    private String exec;

    /**
     * 分公司编码，用于集团指导价页面按分公司展示
     */
    private String orgCode;

}
