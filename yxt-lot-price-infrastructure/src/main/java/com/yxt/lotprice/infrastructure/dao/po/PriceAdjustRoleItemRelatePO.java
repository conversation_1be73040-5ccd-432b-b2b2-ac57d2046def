package com.yxt.lotprice.infrastructure.dao.po;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 分公司/集团的调价/审批人角色和具体的人/机构的关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@TableName("price_adjust_role_item_relate")
public class PriceAdjustRoleItemRelatePO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * role_relate表的编码
     */
    private String parentCode;

    /**
     * 分公司编码，yxt-集团，1006-四川公司，...
     */
    private String companyCode;

    /**
     * 报批人/审批人角色编码
     */
    private String roleCode;

    /**
     * 报批人就是选择的机构，审批人就是选择的员工
     */
    private String roleData;

    /**
     * 角色类型-报批人/审批人关系条目
     */
    private String roleType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 修改人
     */
    private String modifyName;

}
