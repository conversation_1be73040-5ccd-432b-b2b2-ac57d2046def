package com.yxt.lotprice.infrastructure.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/23 15:30
 */
@Data
@TableName("price_adjust_role_item_relate_ext")
public class PriceAdjustRoleItemRelateExtPO implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * item_relate表的主键
     */
    private Long itemRelateId;
    /**
     * 组织编码
     */
    private String orCode;
    /**
     * 员工id
     */
    private String userId;
    /**
     * 所属报批人类别
     */
    private String roleType;
}
