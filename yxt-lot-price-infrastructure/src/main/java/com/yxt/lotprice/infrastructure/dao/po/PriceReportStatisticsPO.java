package com.yxt.lotprice.infrastructure.dao.po;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 调价统计报表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@TableName("price_report_statistics")
public class PriceReportStatisticsPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * ERP商品编码
     */
    private String erpCode;

    /**
     * 分公司编码
     */
    private String companyCode;

    /**
     * 价格类型：（会员价，零售价，慢病价）
     */
    private String priceType;

    /**
     * 审批通过调/定价格
     */
    private BigDecimal price;

    /**
     * 门店数量
     */
    private Integer storeCount;

    /**
     * 门店编码集合
     */
    private String storeInfo;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

}
