package com.yxt.lotprice.infrastructure.dao.po;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 调价申请单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
public class PriceAdjustRoleItemRelateJoinExtPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分公司编码，yxt-集团，1006-四川公司，...
     */
    private String companyCode;

    /**
     * 角色编码
     */
    private String approverRoleCode;

    /**
     * 1.审批人审核的机构编码,roleType为APPROVER才可能有值，有值要做特殊处理
     * 2.当前用户所属机构编码
     */
    private String orgCode;

    /**
     * 当前用户id
     */
    private String userId;


    /**
     * 报批人角色编码
     */
    private String submiterRoleCode;


    /**
     * 当前用户所属角色类型
     */
    private String roleType;


}
