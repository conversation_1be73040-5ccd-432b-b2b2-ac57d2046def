package com.yxt.lotprice.infrastructure.dao.po.update;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Since: 2025/05/27 11:57
 * Author: qs
 */

@Data
public class PriceAdjustResultsUpdatePO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * ERP商品编码
     */
    private String erpCode;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 分公司编码
     */
    private String companyCode;

    /**
     * 价格类型：（会员价，零售价，慢病价）
     */
    private String priceType;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 价格生效开始时间 包含
     */
    private LocalDate startTime;

    /**
     * 价格生效结束时间 包含
     */
    private LocalDate endTime;

    /**
     * 下一次价格
     */
    private BigDecimal nextPrice;

    /**
     * 下一次价格生效开始时间 包含
     */
    private LocalDate nextStartTime;

    /**
     * 下一次价格生效结束时间 包含
     */
    private LocalDate nextEndTime;

    /**
     * 计算出的价格代
     */
    private String priceResultSegment;

    /**
     * 推送 POS 状态
     */
    private String pushPosStatus;

    /**
     * 价格变动记录
     */
    private String priceChangeLog;

    /**
     * 价格变动记录
     */
    private String priceResultLog;

    /**
     * 推送 POS 记录
     */
    private String pushPosLog;


}
