package com.yxt.lotprice.infrastructure.dao.po;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 集团指导价执行记录表--商品门店级
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@TableName("price_adjust_form_item_group_exec_log")
public class PriceAdjustFormItemGroupExecLogPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 调价单明细id
     */
    private Long formItemId;

    /**
     * 门店编码列表
     */
    private String stores;

    /**
     * 执行结果备注
     */
    private String desc;

}
