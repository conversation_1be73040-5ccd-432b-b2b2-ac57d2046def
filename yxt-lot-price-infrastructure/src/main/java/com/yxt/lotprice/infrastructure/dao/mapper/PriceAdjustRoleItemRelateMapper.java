package com.yxt.lotprice.infrastructure.dao.mapper;

import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustRoleItemRelateJoinExtPO;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustRoleItemRelatePO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustRoleItemRelateSearchPO;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface PriceAdjustRoleItemRelateMapper extends BaseMapper<PriceAdjustRoleItemRelatePO> {
    /**
     * 批量插入
     */
    boolean insertBatch(@Param("records")List<PriceAdjustRoleItemRelatePO> records);

    List<PriceAdjustRoleItemRelateJoinExtPO> selectApproverJoinExt(PriceAdjustRoleItemRelateSearchPO searchPO);
}