package com.yxt.lotprice.infrastructure.dao.po;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@TableName("price_adjust_form_auth")
public class PriceAdjustFormAuthPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 调价单编码
     */
    private Long priceAdjustFormId;

    /**
     * 权限值，eg:组织机构路径（用于权限检索）
     */
    private String authData;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

}
