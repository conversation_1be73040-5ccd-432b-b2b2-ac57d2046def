package com.yxt.lotprice.infrastructure.dao.po;

import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 调价申请单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
public class PriceAdjustFormAuthSearchPO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("创建人")
    private String createName;

    @ApiModelProperty("调价单号")
    private String code;

    @ApiModelProperty("审核状态")
    private List<String> auditStatus;

    @ApiModelProperty("审批流项目属性-集团/子公司，COMPANY-公司/GROUP-集团")
    private List<String> scope;

    @ApiModelProperty("报批人类别")
    private List<String> submiterRoleCode;


    @ApiModelProperty("调价类型")
    private List<String> dimensions;

    @ApiModelProperty("审批流名称")
    private String name;

    @ApiModelProperty("作废状态")
    private List<String> cancelStatus;

    @ApiModelProperty("创建人id")
    private String createUserId;

    /**
     * 权限相关查询数据
     */
    private List<PriceAdjustFormSearchAuthFinalPO> authData;
}
