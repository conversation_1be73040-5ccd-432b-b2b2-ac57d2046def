package com.yxt.lotprice.infrastructure.config.sharding;

import com.google.common.collect.Lists;
import com.yxt.lotprice.common.sharding.AbstractShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingValue;

import java.util.*;

/**
 * Since: 2025/05/20 14:23
 * Author: qs
 */
public class AdjustFormItemShardingAlgorithm extends AbstractShardingAlgorithm implements ComplexKeysShardingAlgorithm<String> {
    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, ComplexKeysShardingValue<String> shardingValue) {
        Collection<String> parentIdValues = shardingValue.getColumnNameAndShardingValuesMap().get("parent_code");
        return doSharding(Lists.newArrayList(shardingValue.getLogicTableName()), parentIdValues, shardingValue.getLogicTableName());
    }
}
