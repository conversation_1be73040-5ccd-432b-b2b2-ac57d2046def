package com.yxt.lotprice.infrastructure.dao.po;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 权限下放商品清单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@TableName("price_adjust_doa_commodity")
@Accessors(chain = true)
public class PriceAdjustDoaCommodityPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构id路径
     */
    private String orgPath;

    /**
     * 商品编码
     */
    private String erpCode;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 备注信息
     */
    private String descs;

    /**
     * 商品名称
     */
    private String commodityName;
}
