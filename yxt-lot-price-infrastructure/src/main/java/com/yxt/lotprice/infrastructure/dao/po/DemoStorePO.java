package com.yxt.lotprice.infrastructure.dao.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 慢病门店表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("chronic_store")
public class DemoStorePO implements Serializable {


    private static final long serialVersionUID = 5354238032881982371L;
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 员工数量
     */
    private Integer  staffAmount;

    /**
     * 会员数量
     */
    private Integer  memberAmount;
//
//    /**
//     * 门店编码
//     */
//    private String storeCode;
}
