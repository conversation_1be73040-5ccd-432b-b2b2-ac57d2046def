package com.yxt.lotprice.infrastructure.dao.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustFormAuthSearchPO;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustFormPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface PriceAdjustFormMapper extends BaseMapper<PriceAdjustFormPO> {
    /**
     * 批量插入
     */
    boolean insertBatch(@Param("records")List<PriceAdjustFormPO> records);

    IPage<PriceAdjustFormPO> pageJoinAuth(Page<PriceAdjustFormPO> page, @Param("params") PriceAdjustFormAuthSearchPO searchParams);
}