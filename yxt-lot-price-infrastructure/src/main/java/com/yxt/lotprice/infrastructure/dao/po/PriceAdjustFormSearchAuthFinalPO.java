package com.yxt.lotprice.infrastructure.dao.po;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 调价申请单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
public class PriceAdjustFormSearchAuthFinalPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置的可以审核的部门
     * 针对部分非集团级，分子公司级的需要
     */
    private String orgCode;

    /**
     * 公司编码
     */
    private String companyCode;


    /**
     * 角色编码
     * 放入审批流快照中去查
     */
    private String roleCode;


    /**
     * 机构id路径
     */
    private String parentOrgIdPath;


}
