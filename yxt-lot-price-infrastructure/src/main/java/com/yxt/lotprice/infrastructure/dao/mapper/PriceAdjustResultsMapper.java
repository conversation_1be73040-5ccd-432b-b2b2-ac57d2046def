package com.yxt.lotprice.infrastructure.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustResultsPO;
import com.yxt.lotprice.infrastructure.dao.po.update.PriceAdjustResultsUpdatePO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
public interface PriceAdjustResultsMapper extends BaseMapper<PriceAdjustResultsPO> {
    /**
     * 批量插入
     */
    boolean insertBatch(@Param("records")List<PriceAdjustResultsPO> records);

    List<PriceAdjustResultsPO> selectByErpCodePriceTypeStoreCode(@Param("tableName") String tableName, @Param("storeCodes") Collection<String> storeCodes
            , @Param("erpCode") String erpCode, @Param("priceType") String priceType);

    /**
     * 批量修改价格、价格代
     */
    boolean updatePriceSegment(@Param("records")List<PriceAdjustResultsUpdatePO> records);

    /**
     * 批量修改价格代
     */
    boolean updatePriceSegmentOnly(@Param("records")List<PriceAdjustResultsUpdatePO> records);

    /**
     * 批量修改价格
     */
    boolean updatePrice(@Param("records")List<PriceAdjustResultsUpdatePO> records);
}