package com.yxt.lotprice.infrastructure.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustRoleItemRelateExtPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/23 15:32
 */
public interface PriceAdjustRoleItemRelateExtMapper extends BaseMapper<PriceAdjustRoleItemRelateExtPO> {

    boolean insertBatch(@Param("records") List<PriceAdjustRoleItemRelateExtPO> records);
}
