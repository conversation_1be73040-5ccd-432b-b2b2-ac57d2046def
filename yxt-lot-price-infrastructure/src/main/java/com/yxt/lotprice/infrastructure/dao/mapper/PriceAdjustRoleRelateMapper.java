package com.yxt.lotprice.infrastructure.dao.mapper;

import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustRoleRelatePO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface PriceAdjustRoleRelateMapper extends BaseMapper<PriceAdjustRoleRelatePO> {
    /**
     * 批量插入
     */
    boolean insertBatch(@Param("records")List<PriceAdjustRoleRelatePO> records);
}