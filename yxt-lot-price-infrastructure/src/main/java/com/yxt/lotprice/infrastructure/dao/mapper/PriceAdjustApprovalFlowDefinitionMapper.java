package com.yxt.lotprice.infrastructure.dao.mapper;

import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustApprovalFlowDefinitionPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface PriceAdjustApprovalFlowDefinitionMapper extends BaseMapper<PriceAdjustApprovalFlowDefinitionPO> {
    /**
     * 批量插入
     */
    boolean insertBatch(@Param("records")List<PriceAdjustApprovalFlowDefinitionPO> records);
}