package com.yxt.lotprice.common.sharding;

import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Since: 2025/05/20 18:05
 * Author: qs
 */
public abstract class AbstractShardingAlgorithm {

    public static final String split = "_";
    // 虚拟节点数，可配置
    protected int virtualNum = 1024;
    // 物理表数，可配置
    protected int physicalNum = 16;
    // 批量路由阈值
    protected int bulkThreshold = 200;
    @Getter
    protected Properties props = new Properties();

    public Collection<String> doSharding(Collection<String> availableTargetNames, Collection<String> keyColumValues, String logicTableName) {
        // 如果没有提供分片值，返回所有表（应避免这种情况）
        if (CollectionUtils.isEmpty(keyColumValues)) {
            return availableTargetNames;
        }

        // 判断是否触发批量模式
        if (keyColumValues.size() > bulkThreshold) {
            return availableTargetNames;
        }

        return getSpecificTables(availableTargetNames, logicTableName, keyColumValues);
    }

    /**
     * 根据分表键获取表
     * @param availableTargetNames 所有物理表名
     * @param logicTableName 逻辑表名
     * @param keyColumValues 分表键集合
     * @return 路由到的所有表
     */
    private Collection<String> getSpecificTables(Collection<String> availableTargetNames, String logicTableName, Collection<String> keyColumValues) {
        Set<String> availableSet = new HashSet<>(availableTargetNames);
        return keyColumValues.stream().map(keyColumValue -> logicTableName + split + CommonCalculateSuffix.calculateTableSuffix(keyColumValue, virtualNum, physicalNum))
                .filter(availableSet::contains)
                .collect(Collectors.toCollection(LinkedHashSet::new));
    }

    public void init(Properties properties) {
        this.props = properties;
        // 从配置中初始化参数
        if (properties != null) {
            this.virtualNum = Integer.parseInt(properties.getProperty("virtual_num", String.valueOf(this.virtualNum)));
            this.physicalNum = Integer.parseInt(properties.getProperty("physical_num", String.valueOf(this.physicalNum)));
            this.bulkThreshold = Integer.parseInt(properties.getProperty("bulk_threshold", String.valueOf(this.bulkThreshold)));
        }
    }

}
