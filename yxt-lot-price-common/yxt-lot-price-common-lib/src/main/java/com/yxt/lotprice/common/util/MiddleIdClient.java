package com.yxt.lotprice.common.util;

import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.IdUtil;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lang.util.ExLogger;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version : MiddleIdClient.java,v 0.1 2022年06月09日 15:19
 */
@Service
@Slf4j
public class MiddleIdClient {

    @Resource
    private RedissonClient redissonClient;


    @Value("${spring.application.name: default}")
    private String applicationName;

    private static volatile Long workId;


    /**
     * 获取分布自增ID
     *
     * @param batch 一次匹配获取多少个
     * @return 返回多少个
     */
    public List<Long> getId(int batch) {
        try {
            return doGet(batch);
        } catch (Exception e) {
            // 出现异常兜底
            ExLogger.logger("MiddleIdClient").error("触发兜底逻辑", e);
//            WxRobotOkHttpUtils.post(CommodityRobotConfig.GOODS_COMMON_ALERT, "MiddleIdClient，获取失败 触发兜底逻辑", e);
            return genIds(batch);
        }
    }

    private List<Long> doGet(int batch) throws InterruptedException {
        RLock rLock = redissonClient.getLock(applicationName + ":" + "MIDDLE_ID_GEN");
        try {
            // 分布式锁
            boolean tryLock = rLock.tryLock(20, 20, TimeUnit.SECONDS);
            if (!tryLock) {
                // 没有获取到锁超时后  继续让生成id
                ExLogger.logger("MiddleIdClient").error("尝试加锁失败");
                throw new YxtBizException("尝试加锁失败");
            }
            return genIds(batch);
        } catch (Exception e) {
            ExLogger.logger("MiddleIdClient").error("尝试加锁失败", e);
//            WxRobotOkHttpUtils.post(CommodityRobotConfig.GOODS_COMMON_ALERT, "MiddleIdClient，尝试加锁失败", e);
            throw e;
        } finally {
            // 释放锁
            if (rLock.isLocked()) {
                if (rLock.isHeldByCurrentThread()){
                    rLock.unlock();
                }
            } else {
                log.warn("thread:{},持有锁超时被释放", Thread.currentThread().getName());
            }
        }
    }

    private List<Long> genIds(int batch) {
        List<Long> ids = new ArrayList<>();
        // 生产id
        for (int i = 0; i < batch; i++) {
            ids.add(IdUtil.getSnowflake(getWorkerId(), 1L).nextId());
        }
        return ids;
    }

    private static synchronized long getWorkerId() {
        if (workId == null) {
            workId = NetUtil.ipv4ToLong(NetUtil.getLocalhostStr()) % 10;
//            WxRobotOkHttpUtils.post(CommodityRobotConfig.GOODS_COMMON_ALERT, "MiddleIdClient，workId ", workId);
        }
        return workId;
    }
}