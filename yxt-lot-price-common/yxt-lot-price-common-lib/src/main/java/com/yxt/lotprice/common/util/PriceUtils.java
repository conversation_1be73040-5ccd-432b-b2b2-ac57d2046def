package com.yxt.lotprice.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Since: 2025/05/28 10:24
 * Author: qs
 */
public class PriceUtils {

    /**
     * 统一价格四舍五入保留两位小数方法转 string
     * 标准四舍五入并转为字符串（保留两位小数）
     * @param value 要处理的数值
     * @return 格式化后的字符串（如：123.46）
     */
    public static String roundToPriceString(BigDecimal value) {
        return roundToPrice(value).toString();
    }

    /**
     * 统一价格四舍五入保留两位小数方法
     * 标准四舍五入并转为字符串（保留两位小数）
     * @param value 要处理的数值
     * @return 格式化后的字符串（如：123.46）
     */
    public static BigDecimal roundToPrice(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.setScale(2, RoundingMode.HALF_UP);
    }

}
