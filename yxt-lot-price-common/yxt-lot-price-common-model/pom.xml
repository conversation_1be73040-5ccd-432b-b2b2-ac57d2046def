<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yxt.lotprice</groupId>
        <artifactId>yxt-lot-price-common</artifactId>
        <version>${reversion}</version>
    </parent>

    <artifactId>yxt-lot-price-common-model</artifactId>

    <properties>
        <module.deploy.skip>false</module.deploy.skip>
    </properties>

    <dependencies>
        <!-- swagger -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- common-lang -->
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-lang</artifactId>
            <scope>provided</scope>
        </dependency>


    </dependencies>


</project>
