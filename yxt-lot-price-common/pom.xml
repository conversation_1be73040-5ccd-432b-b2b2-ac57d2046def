<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-lot-price-common</artifactId>
    <packaging>pom</packaging>
    <!--    <version>${reversion}</version>-->
    <name>yxt-lot-price-common</name>
    <description>yxt-lot-price-common</description>
    <modules>
        <module>yxt-lot-price-common-model</module>
        <module>yxt-lot-price-common-lib</module>
    </modules>

    <parent>
        <groupId>com.yxt.lotprice</groupId>
        <artifactId>yxt-lot-price</artifactId>
        <version>${reversion}</version>
    </parent>

    <properties>
        <module.deploy.skip>false</module.deploy.skip>
    </properties>
</project>
