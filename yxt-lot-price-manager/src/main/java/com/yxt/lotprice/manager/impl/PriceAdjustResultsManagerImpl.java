package com.yxt.lotprice.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustResultsMapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustResultsPO;
import com.yxt.lotprice.infrastructure.dao.po.update.PriceAdjustResultsUpdatePO;
import com.yxt.lotprice.service.manager.iface.PriceAdjustResultsManager;
import com.yxt.lotprice.service.model.bo.PriceBO;
import com.yxt.lotprice.service.model.bo.PriceResultsBO;
import com.yxt.lotprice.service.model.bo.PriceResultsUpdateBO;
import com.yxt.lotprice.service.model.bo.log.PriceChangeLogBO;
import com.yxt.lotprice.service.model.bo.log.PriceResultLogBO;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import com.yxt.lotprice.service.model.enums.PushStatusEnum;
import com.yxt.lotprice.service.model.enums.log.PriceCalculateSourceEnum;
import com.yxt.lotprice.service.model.enums.log.PriceSourceEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Since: 2025/05/23 14:16
 * Author: qs
 */

@Service
public class PriceAdjustResultsManagerImpl implements PriceAdjustResultsManager {

    @Resource
    private PriceAdjustResultsMapper priceAdjustResultsMapper;

    @Override
    public List<PriceResultsBO> listByErpCodePriceTypeStoreCode(String tableName, Collection<String> companyCodes, Collection<String> storeCodes, String erpCode, PriceTypeEnum priceType) {
        if (StringUtils.isEmpty(tableName)) {
            return BeanUtil.copyToList(priceAdjustResultsMapper.selectList(new LambdaQueryWrapper<PriceAdjustResultsPO>()
                    // 不查询日志记录
                    .select(info -> !info.getColumn().equals("price_result_log") &&
                            !info.getColumn().equals("price_change_log") &&
                            !info.getColumn().equals("push_pos_log"))
                    .in(PriceAdjustResultsPO::getCompanyCode, companyCodes)
                    .eq(PriceAdjustResultsPO::getErpCode, erpCode)
                    .eq(PriceAdjustResultsPO::getPriceType, priceType)
                    .in(PriceAdjustResultsPO::getStoreCode, storeCodes)
            ), PriceResultsBO.class);
        } else {
            return BeanUtil.copyToList(priceAdjustResultsMapper.selectByErpCodePriceTypeStoreCode(tableName, storeCodes, erpCode, priceType.name()), PriceResultsBO.class);
        }
    }

    @Override
    public List<PriceResultsBO> listByStoreCodePriceTypeErpCodes(String companyCode, String storeCode, PriceTypeEnum priceType, Collection<String> erpCodes) {
        return BeanUtil.copyToList(priceAdjustResultsMapper.selectList(new LambdaQueryWrapper<PriceAdjustResultsPO>()
                // 不查询日志记录
                .select(info -> !info.getColumn().equals("price_result_log") &&
                        !info.getColumn().equals("price_change_log") &&
                        !info.getColumn().equals("push_pos_log"))
                .eq(PriceAdjustResultsPO::getCompanyCode, companyCode)
                .eq(PriceAdjustResultsPO::getStoreCode, storeCode)
                .eq(PriceAdjustResultsPO::getPriceType, priceType)
                .in(PriceAdjustResultsPO::getErpCode, erpCodes)
        ), PriceResultsBO.class);

    }

    @Override
    public void saveBatch(Collection<PriceResultsBO> inserts) {
        List<PriceAdjustResultsPO> insertList = new ArrayList<>();
        inserts.forEach(e -> {
            PriceAdjustResultsPO po = new PriceAdjustResultsPO();
            BeanUtils.copyProperties(e, po);
            po.setPushPosStatus(PushStatusEnum.NOT.name());
            // 首次不添加记录数据 todo qs 测试验证是否需要保存空数组
            po.setPriceChangeLog(JSON.toJSONString(Collections.EMPTY_LIST));
            po.setPriceResultLog(JSON.toJSONString(Collections.EMPTY_LIST));
            po.setPushPosLog(JSON.toJSONString(Collections.EMPTY_LIST));
            insertList.add(po);
        });

        priceAdjustResultsMapper.insertBatch(insertList);
    }

    @Override
    public void updatePriceSegment(Collection<PriceResultsUpdateBO> updates, PriceSourceEnum sourceEnum) {
        List<PriceAdjustResultsUpdatePO> updateList = new ArrayList<>();
        updates.forEach(e -> {
            PriceAdjustResultsUpdatePO po = new PriceAdjustResultsUpdatePO();
            BeanUtils.copyProperties(e, po);
            po.setPushPosStatus(PushStatusEnum.NOT.name());
            // 价格结果日志
            PriceResultLogBO priceResultLogBO = buildPriceResultLog(sourceEnum, e);
            po.setPriceResultLog(JSON.toJSONString(priceResultLogBO));
            // 价格变化日志
            PriceChangeLogBO priceChangeLogBO = buildPriceChangeLog(sourceEnum, e);
            po.setPriceChangeLog(JSON.toJSONString(priceChangeLogBO));
            updateList.add(po);
        });

        priceAdjustResultsMapper.updatePriceSegment(updateList);
    }

    private static PriceResultLogBO buildPriceResultLog(PriceSourceEnum sourceEnum, PriceResultsUpdateBO e) {
        PriceResultLogBO priceResultLogBO = new PriceResultLogBO();
        priceResultLogBO.setSource(sourceEnum.name());
        priceResultLogBO.setCreateTime(LocalDateTime.now());
        priceResultLogBO.setPriceResultSegment(e.getPriceResultSegment());
        return priceResultLogBO;
    }

    @Override
    public void updatePriceSegmentOnly(Collection<PriceResultsUpdateBO> updates, PriceSourceEnum sourceEnum) {
        List<PriceAdjustResultsUpdatePO> updateList = new ArrayList<>();
        updates.forEach(e -> {
            PriceAdjustResultsUpdatePO po = new PriceAdjustResultsUpdatePO();
            BeanUtils.copyProperties(e, po);
            po.setPushPosStatus(PushStatusEnum.NOT.name());
            // 价格结果日志
            PriceResultLogBO priceResultLogBO = buildPriceResultLog(sourceEnum, e);
            po.setPriceResultLog(JSON.toJSONString(priceResultLogBO));
            updateList.add(po);
        });

        priceAdjustResultsMapper.updatePriceSegmentOnly(updateList);
    }

    @Override
    public void updatePrice(Collection<PriceResultsUpdateBO> updates, PriceSourceEnum sourceEnum) {
        List<PriceAdjustResultsUpdatePO> updateList = new ArrayList<>();
        updates.forEach(e -> {
            PriceAdjustResultsUpdatePO po = new PriceAdjustResultsUpdatePO();
            BeanUtils.copyProperties(e, po);
            po.setPushPosStatus(PushStatusEnum.NOT.name());
            // 价格变化日志
            PriceChangeLogBO priceChangeLogBO = buildPriceChangeLog(sourceEnum, e);
            po.setPriceChangeLog(JSON.toJSONString(priceChangeLogBO));
            updateList.add(po);
        });

        priceAdjustResultsMapper.updatePrice(updateList);
    }

    private static PriceChangeLogBO buildPriceChangeLog(PriceSourceEnum sourceEnum, PriceResultsUpdateBO e) {
        PriceChangeLogBO priceChangeLogBO = new PriceChangeLogBO();
        priceChangeLogBO.setSource(sourceEnum.name());
        priceChangeLogBO.setCreateTime(LocalDateTime.now());
        priceChangeLogBO.setOldPrice(e.getOldPrice());
        priceChangeLogBO.setNewPrice(new PriceBO(e.getPrice(), e.getStartTime(), e.getEndTime(), e.getNextPrice(), e.getNextStartTime(), e.getNextEndTime()));
        return priceChangeLogBO;
    }


}
