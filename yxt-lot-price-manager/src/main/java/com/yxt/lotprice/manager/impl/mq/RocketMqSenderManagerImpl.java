package com.yxt.lotprice.manager.impl.mq;

import com.yxt.lang.util.ExLogger;
import com.yxt.lotprice.service.manager.mq.MqSenderManager;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * Since: 2025/05/28 11:41
 * Author: qs
 */

@Service
public class RocketMqSenderManagerImpl implements MqSenderManager {

    // 默认按每批消息不超过2M 2 * 1024 * 1024
    @Value("${biz.mq.msg-max-batch-size:2097152}")
    public Integer maxBatchSize;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    public void sendBatchMessages(List<String> contents, String topic, String tag) {
        // 1. 构造 Message 对象列表
        List<Message> messageList = new ArrayList<>();
        for (String content : contents) {
            Message message = new Message(
                    topic,
                    // tag 可选
                    tag,
                    content.getBytes(StandardCharsets.UTF_8)
            );
            messageList.add(message);
        }

        // 按照消息大小，做简单分批
        List<List<Message>> batches = splitBatch(messageList, maxBatchSize);

        for (List<Message> batch : batches) {
            try {
                rocketMQTemplate.getProducer().send(batch);
            } catch (MQClientException | RemotingException | MQBrokerException | InterruptedException e) {
                ExLogger.logger().error("rocket mq 消息批量发送异常", e);
                throw new RuntimeException(e);
            }
        }
    }

    // 按总大小拆分批次（消息体大小 + 额外头部估算）
    private List<List<Message>> splitBatch(List<Message> messages, int maxBatchSize) {
        List<List<Message>> result = new ArrayList<>();
        List<Message> currentBatch = new ArrayList<>();
        int currentSize = 0;

        for (Message msg : messages) {
            int msgSize = msg.getBody().length + 100; // 加个头部估算
            if (currentSize + msgSize > maxBatchSize) {
                result.add(currentBatch);
                currentBatch = new ArrayList<>();
                currentSize = 0;
            }
            currentBatch.add(msg);
            currentSize += msgSize;
        }

        if (!currentBatch.isEmpty()) {
            result.add(currentBatch);
        }

        return result;
    }

}
