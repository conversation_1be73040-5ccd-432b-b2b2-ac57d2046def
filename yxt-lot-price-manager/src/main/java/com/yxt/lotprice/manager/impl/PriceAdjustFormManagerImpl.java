package com.yxt.lotprice.manager.impl;

import com.alibaba.fastjson.JSON;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lotprice.common.util.MiddleIdClient;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustFormMapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustApprovalFlowDefinitionPO;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustFormAuthSearchPO;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustFormPO;
import com.yxt.lotprice.manager.convert.PageUtil;
import com.yxt.lotprice.service.manager.iface.PriceAdjustFormManager;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormBO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormSearchBO;
import com.yxt.lotprice.service.model.dto.b.request.InvalidAdjustFormReq;
import com.yxt.lotprice.service.model.dto.b.request.PriceAdjustFormSaveReq;
import com.yxt.lotprice.service.model.enums.AdjustFormAuditStatus;
import com.yxt.lotprice.service.model.enums.CancelStatusEnum;
import com.yxt.lotprice.service.utils.SequenceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/26 16:03
 */
@Service
@Slf4j
public class PriceAdjustFormManagerImpl implements PriceAdjustFormManager {

    @Resource
    private SequenceUtil sequenceUtil;

    @Resource
    private MiddleIdClient middleIdClient;

    @Resource
    private PriceAdjustFormMapper priceAdjustFormMapper;
    @Override
    public String savePriceAdjustForm(PriceAdjustFormSaveReq req, String userName,String userId) {
        PriceAdjustFormPO po = new PriceAdjustFormPO();
        String tjSequence = sequenceUtil.getTJSequence();
        po.setCode(tjSequence);
        po.setId(middleIdClient.getId(1).get(0));
        po.setName(req.getName());
        po.setScope(req.getScope().name());
        po.setDimension(req.getDimension().name());
        po.setCancelStatus(CancelStatusEnum.NOT_CANCELED.name());
        LocalDateTime now = LocalDateTime.now();
        po.setCreateTime(now);
        po.setModifyTime(now);
        po.setCreateName(userName);
        po.setModifyName(userName);
        po.setCreateUserId(Integer.parseInt(userId));
        po.setRemark(req.getRemark());
        po.setPriceOpsType(req.getAdjustType().name());
        po.setStatus(req.getStatus().name());
        if (req.getApprovalFlowDefinition() != null && req.getStatus() == AdjustFormAuditStatus.SUBMITTED){
            po.setFlowDefinition(BeanUtil.copyProperties(req.getApprovalFlowDefinition(), PriceAdjustApprovalFlowDefinitionPO.class));
            po.setFlowDefinitionId(req.getApprovalFlowDefinition().getId());
        }
        priceAdjustFormMapper.insert(po);

        req.setId(po.getId());
        req.setCode(po.getCode());
        return tjSequence;
    }

    @Override
    public Boolean updatePriceAdjustForm(PriceAdjustFormSaveReq req, String userName, String userId) {
        LambdaQueryWrapper<PriceAdjustFormPO> wrapper = new LambdaQueryWrapper<PriceAdjustFormPO>().eq(PriceAdjustFormPO::getCode, req.getCode());
        PriceAdjustFormPO po = priceAdjustFormMapper.selectOne(wrapper);
        if (po == null){
            throw new YxtBizException("调价单不存在");
        }
        po.setName(req.getName());
        po.setScope(req.getScope().name());
        po.setDimension(req.getDimension().name());
        po.setPriceOpsType(req.getAdjustType().name());
        po.setStatus(req.getStatus().name());
        po.setCreateUserRole(req.getCreateUserRole().name());
        po.setModifyTime(LocalDateTime.now());
        po.setModifyName(userName);
        po.setRemark(req.getRemark());
        if (req.getApprovalFlowDefinition() != null && req.getStatus() == AdjustFormAuditStatus.SUBMITTED){
            po.setFlowDefinition(BeanUtil.copyProperties(req.getApprovalFlowDefinition(), PriceAdjustApprovalFlowDefinitionPO.class));
            po.setFlowDefinitionId(req.getApprovalFlowDefinition().getId());
        }
        priceAdjustFormMapper.updateById(po);
        return Boolean.TRUE;
    }

    @Override
    public PageDTO<PriceAdjustFormBO> page(PriceAdjustFormSearchBO searchBO) {
        IPage<PriceAdjustFormPO> page = priceAdjustFormMapper.selectPage(
                new Page<>(searchBO.getCurrentPage(), searchBO.getPageSize())
                , Wrappers.<PriceAdjustFormPO>lambdaQuery()
                        .eq(StringUtils.isNotBlank(searchBO.getCreateUserId()), PriceAdjustFormPO::getCreateUserId, searchBO.getCreateUserId())
                        .eq(StringUtils.isNotBlank(searchBO.getCode()), PriceAdjustFormPO::getCode, searchBO.getCode())
                        .ge(searchBO.getStartTime() != null, PriceAdjustFormPO::getCreateTime, searchBO.getStartTime())
                        .le(searchBO.getEndTime() != null, PriceAdjustFormPO::getCreateTime, searchBO.getEndTime())
                        .in(CollectionUtils.isNotEmpty(searchBO.getAuditStatus()), PriceAdjustFormPO::getStatus, searchBO.getAuditStatus())
                        .in(CollectionUtils.isNotEmpty(searchBO.getScope()), PriceAdjustFormPO::getScope, searchBO.getScope())
                        .in(CollectionUtils.isNotEmpty(searchBO.getDimensions()), PriceAdjustFormPO::getDimension, searchBO.getDimensions())
                        .in(CollectionUtils.isNotEmpty(searchBO.getSubmiterRoleCode()), PriceAdjustFormPO::getCreateUserRole, searchBO.getSubmiterRoleCode())
                        .in(CollectionUtils.isNotEmpty(searchBO.getCancelStatus()), PriceAdjustFormPO::getCancelStatus, searchBO.getCancelStatus())
                        .like(StringUtils.isNotBlank(searchBO.getName()), PriceAdjustFormPO::getName, searchBO.getName())
                        .like(StringUtils.isNotBlank(searchBO.getCreateName()), PriceAdjustFormPO::getCreateName, searchBO.getCreateName())
                        .orderByDesc(PriceAdjustFormPO::getModifyName)
        );
        return PageUtil.convertToPageDTO(page, PriceAdjustFormBO.class);
    }

    @Override
    public PriceAdjustFormBO getPriceAdjustFormByCode(String code) {
        LambdaQueryWrapper<PriceAdjustFormPO> wrapper = new LambdaQueryWrapper<PriceAdjustFormPO>().eq(PriceAdjustFormPO::getCode, code);
        PriceAdjustFormPO po = priceAdjustFormMapper.selectOne(wrapper);
        if (po == null){
            return null;
        }
        PriceAdjustFormBO bo = new PriceAdjustFormBO();

        BeanUtils.copyProperties(po,bo);
        bo.setFlowDefinition(JSON.toJSONString(po.getFlowDefinition()));
        return bo;
    }

    @Override
    public Boolean invalidPriceAdjustForm(InvalidAdjustFormReq req, String userName, String userId) {
        LambdaUpdateWrapper<PriceAdjustFormPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PriceAdjustFormPO::getCode, req.getCode());
        wrapper.set(PriceAdjustFormPO::getCancelStatus, CancelStatusEnum.FULLY_CANCELED.name());
        wrapper.set(PriceAdjustFormPO::getRemark, req.getRemark());
        wrapper.set(PriceAdjustFormPO::getModifyTime, LocalDateTime.now());
        wrapper.set(PriceAdjustFormPO::getModifyName, userName);

        priceAdjustFormMapper.update(null, wrapper);
        return Boolean.TRUE;
    }

    @Override
    public PageDTO<PriceAdjustFormBO> pageJoinAuth(PriceAdjustFormSearchBO bo) {
        PriceAdjustFormAuthSearchPO po = BeanUtil.copyProperties(bo, PriceAdjustFormAuthSearchPO.class);
        IPage<PriceAdjustFormPO> page = priceAdjustFormMapper.pageJoinAuth(new Page<>(bo.getCurrentPage(), bo.getPageSize()), po);
        return PageUtil.convertToPageDTO(page, PriceAdjustFormBO.class);
    }
}
