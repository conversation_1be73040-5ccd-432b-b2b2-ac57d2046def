package com.yxt.lotprice.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.infrastructure.dao.mapper.DemoChronicStoreMapper;
import com.yxt.lotprice.infrastructure.dao.po.DemoStorePO;
import com.yxt.lotprice.manager.convert.PageUtil;
import com.yxt.lotprice.service.DemoChoincStoreService;
import com.yxt.lotprice.service.manager.iface.DemoChonicStoreManager;
import com.yxt.lotprice.service.model.dto.b.request.DemoBReq;
import com.yxt.lotprice.service.model.dto.b.response.DemoBResp;
import com.yxt.lotprice.service.model.dto.third.request.DemoThirdReq;
import com.yxt.lotprice.service.model.dto.third.response.DemoThirdResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DemoChonicStoreManagerImpl implements DemoChonicStoreManager {

    @Autowired
    DemoChronicStoreMapper mapper;

    @Override
    public PageDTO<DemoThirdResp> page(DemoThirdReq req) {
        IPage<DemoStorePO> demoStorePOIPage = mapper.selectPage(new Page<>(req.getPage(), req.getSize()), Wrappers.lambdaQuery(new DemoStorePO()).lt(DemoStorePO::getStaffAmount, req.getMaxMemberAmount()));
        PageDTO<DemoThirdResp> demoThirdRespPageDTO = PageUtil.convertToPageDTO(demoStorePOIPage, DemoThirdResp.class);
        return demoThirdRespPageDTO;
    }

    @Override
    public List<DemoBResp> list(DemoBReq req) {
        List<DemoStorePO> list = mapper.selectList(Wrappers.lambdaQuery(new DemoStorePO()).lt(DemoStorePO::getStaffAmount, req.getMaxStaffAmount()));
        List<DemoBResp> resultList = BeanUtil.copyToList(list, DemoBResp.class);
        return resultList;
    }
}
