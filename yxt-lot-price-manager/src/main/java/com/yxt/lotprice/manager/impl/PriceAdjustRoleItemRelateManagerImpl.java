package com.yxt.lotprice.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lotprice.common.util.MiddleIdClient;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustRoleItemRelateExtMapper;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustRoleItemRelateMapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustRoleItemRelateExtPO;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustRoleItemRelateJoinExtPO;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustRoleItemRelatePO;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustRoleItemRelateSearchPO;
import com.yxt.lotprice.manager.convert.PageUtil;
import com.yxt.lotprice.service.cache.OrgInfoCacheUtils;
import com.yxt.lotprice.service.manager.iface.PriceAdjustRoleItemRelateManager;
import com.yxt.lotprice.service.model.bo.PriceAdjustRoleItemRelateBO;
import com.yxt.lotprice.service.model.dto.b.request.*;
import com.yxt.lotprice.service.model.dto.b.response.*;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationDTO;
import com.yxt.lotprice.service.model.enums.AdjustRoleDimensionType;
import com.yxt.lotprice.service.model.enums.AdjustRoleType;
import com.yxt.lotprice.service.model.enums.AdjustScopeType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/20 11:53
 */
@Service
@Slf4j
public class PriceAdjustRoleItemRelateManagerImpl implements PriceAdjustRoleItemRelateManager {

    @Resource
    private PriceAdjustRoleItemRelateMapper priceAdjustRoleItemRelateMapper;
    @Resource
    private MiddleIdClient middleIdClient;
    @Autowired
    private PriceAdjustRoleItemRelateExtMapper priceAdjustRoleItemRelateExtMapper;

    @Override
    public Boolean addSubmitterItem(AddOrEditSubmitterReq req, String userName) {
        for (ItemRelateCompanyReq itemReq : req.getCompanyList()) {
            PriceAdjustRoleItemRelatePO po = new PriceAdjustRoleItemRelatePO();
            po.setId(middleIdClient.getId(1).get(0));
            po.setParentCode(req.getCode());
            if (req.getScopeName().equals(AdjustScopeType.COMPANY.name())){
                SysOrganizationDTO sysOrganizationDTO = OrgInfoCacheUtils.getCompanyByOrCode(itemReq.getRoleData());
                if (sysOrganizationDTO != null){
                    po.setCompanyCode(sysOrganizationDTO.getOrCode());
                }
            }
            po.setRoleCode(req.getRoleCode());
            po.setRoleData(itemReq.getRoleData());
            po.setRoleType(AdjustRoleDimensionType.SUBMITTER.name());
            po.setCreateName(userName);
            po.setModifyName(userName);
            po.setCreateTime(LocalDateTime.now());
            po.setModifyTime(LocalDateTime.now());
            priceAdjustRoleItemRelateMapper.insert(po);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateSubmitterItem(AddOrEditSubmitterReq req, String userName) {
        LambdaQueryWrapper<PriceAdjustRoleItemRelatePO> wrapper = new LambdaQueryWrapper<PriceAdjustRoleItemRelatePO>().eq(PriceAdjustRoleItemRelatePO::getParentCode, req.getCode());
        priceAdjustRoleItemRelateMapper.delete(wrapper);
        addSubmitterItem(req,  userName);
        return Boolean.TRUE;
    }

    @Override
    public Boolean addApproverItem(AddOrEditApproverReq req, String userName) {
        for (ItemRelateApproverReq itemReq : req.getApproverList()) {
            PriceAdjustRoleItemRelatePO po = new PriceAdjustRoleItemRelatePO();
            po.setId(middleIdClient.getId(1).get(0));
            po.setParentCode(req.getCode());
            po.setCompanyCode(req.getCompanyCode());
            po.setRoleCode(itemReq.getRoleCode());

            po.setRoleType(AdjustRoleDimensionType.APPROVER.name());
            po.setCreateName(userName);
            po.setModifyName(userName);
            po.setCreateTime(LocalDateTime.now());
            po.setModifyTime(LocalDateTime.now());
            priceAdjustRoleItemRelateMapper.insert(po);

            List<RelateExtReq> relateExtList = itemReq.getRelateExtList();
            if (CollUtil.isNotEmpty(relateExtList)){
                List<PriceAdjustRoleItemRelateExtPO> relateExtPOList = new ArrayList<>();
                relateExtList.forEach(item -> {
                    PriceAdjustRoleItemRelateExtPO relateExtPO = new PriceAdjustRoleItemRelateExtPO();
                    relateExtPO.setItemRelateId(po.getId());
                    relateExtPO.setOrCode(item.getOrCode());
                    relateExtPO.setUserId(item.getUserId());
                    if (AdjustScopeType.COMPANY.name().equals(req.getScopeName())){
                        relateExtPO.setRoleType(AdjustRoleType.BRANCH_S_1.name());
                    }else {
                        relateExtPO.setRoleType(item.getRoleType());
                    }
                    relateExtPOList.add(relateExtPO);
                });
                priceAdjustRoleItemRelateExtMapper.insertBatch(relateExtPOList);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateApproverItem(AddOrEditApproverReq req, String userName) {
        LambdaQueryWrapper<PriceAdjustRoleItemRelatePO> wrapper = new LambdaQueryWrapper<PriceAdjustRoleItemRelatePO>().eq(PriceAdjustRoleItemRelatePO::getParentCode, req.getCode());
        List<PriceAdjustRoleItemRelatePO> priceAdjustRoleItemRelatePOS = priceAdjustRoleItemRelateMapper.selectList(wrapper);
        List<Long> roleItemRelateIds = priceAdjustRoleItemRelatePOS.stream().map(PriceAdjustRoleItemRelatePO::getId).collect(Collectors.toList());

        priceAdjustRoleItemRelateMapper.delete(wrapper);

        LambdaQueryWrapper<PriceAdjustRoleItemRelateExtPO> extWrapper = new LambdaQueryWrapper<PriceAdjustRoleItemRelateExtPO>()
                .in(PriceAdjustRoleItemRelateExtPO::getItemRelateId, roleItemRelateIds);
        priceAdjustRoleItemRelateExtMapper.delete(extWrapper);
        addApproverItem(req,  userName);
        return Boolean.TRUE;
    }

    @Override
    public List<ItemRelateSubmitterResp> getSubmitterItemList(String code,String scopeName) {
        List<PriceAdjustRoleItemRelatePO> list = priceAdjustRoleItemRelateMapper.selectList(new LambdaQueryWrapper<PriceAdjustRoleItemRelatePO>().eq(PriceAdjustRoleItemRelatePO::getParentCode, code));
        if (CollUtil.isEmpty(list)){
            throw new YxtBizException("未找到报批人组织配置");
        }
        List<ItemRelateSubmitterResp> itemRelateSubmitterRespList = new ArrayList<>();
        if (AdjustScopeType.GROUP.name().equals(scopeName)){
            for (PriceAdjustRoleItemRelatePO priceAdjustRoleItemRelatePO : list) {
                ItemRelateSubmitterResp resp = new ItemRelateSubmitterResp();

                SysOrganizationDTO company = OrgInfoCacheUtils.getStoreListByCode(priceAdjustRoleItemRelatePO.getCompanyCode());
                if (company != null) {
                    resp.setCompanyName(company.getOrName());
                    resp.setCompanyCode(company.getOrCode());
                }
                SysOrganizationDTO org = OrgInfoCacheUtils.getStoreListByCode(priceAdjustRoleItemRelatePO.getRoleData());
                if (org != null) {
                    resp.setOrgName(org.getOrName());
                    resp.setRoleData(org.getOrCode());
                }
                itemRelateSubmitterRespList.add(resp);
            }
        }else {
            for (PriceAdjustRoleItemRelatePO priceAdjustRoleItemRelatePO : list) {
                ItemRelateSubmitterResp resp = new ItemRelateSubmitterResp();
                SysOrganizationDTO company = OrgInfoCacheUtils.getStoreListByCode(priceAdjustRoleItemRelatePO.getCompanyCode());
                if (company != null) {
                    resp.setCompanyName(company.getOrName());
                    resp.setCompanyCode(company.getOrCode());
                }
                SysOrganizationDTO org = OrgInfoCacheUtils.getStoreListByCode(priceAdjustRoleItemRelatePO.getRoleData());
                if (org != null) {
                    resp.setOrgName(org.getOrName());
                    resp.setRoleData(org.getOrCode());
                    SysOrganizationDTO branch = OrgInfoCacheUtils.getBranchByOrCode(org.getOrCode());
                    if (branch != null) {
                        resp.setBranchName(branch.getOrName());
                        resp.setBranchCode(branch.getOrCode());
                    }
                }
                itemRelateSubmitterRespList.add(resp);
            }
        }

        return itemRelateSubmitterRespList;
    }

    @Override
    public List<RepeatItemDTO> checkSubmitterList(String roleCode, List<String> orgCode) {
        LambdaQueryWrapper<PriceAdjustRoleItemRelatePO> queryWrapper = new LambdaQueryWrapper<PriceAdjustRoleItemRelatePO>()
                .ne(PriceAdjustRoleItemRelatePO::getRoleCode, roleCode)
                .in(PriceAdjustRoleItemRelatePO::getRoleData, orgCode)
                .eq(PriceAdjustRoleItemRelatePO::getRoleType, AdjustRoleDimensionType.SUBMITTER);
        List<PriceAdjustRoleItemRelatePO> priceAdjustRoleItemRelatePOS = priceAdjustRoleItemRelateMapper.selectList(queryWrapper);
        List<RepeatItemDTO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(priceAdjustRoleItemRelatePOS)){
            for (PriceAdjustRoleItemRelatePO priceAdjustRoleItemRelatePO : priceAdjustRoleItemRelatePOS) {
                RepeatItemDTO resp = new RepeatItemDTO();
                resp.setOrCode(priceAdjustRoleItemRelatePO.getRoleData());
                SysOrganizationDTO storeListByCode = OrgInfoCacheUtils.getStoreListByCode(priceAdjustRoleItemRelatePO.getRoleData());
                if (storeListByCode != null){
                    resp.setOrName(storeListByCode.getOrName());
                }
                resp.setRoleType(priceAdjustRoleItemRelatePO.getRoleCode());
                list.add(resp);
            }
        }
        return list;
    }

    @Override
    public PageDTO<ToEditPageResp> getSubmitterOrgList(ToEditPageReq req) {
        LambdaQueryWrapper<PriceAdjustRoleItemRelatePO> queryWrapper = new LambdaQueryWrapper<>();
        if (AdjustScopeType.COMPANY.name().equals(req.getScopeName())){
            queryWrapper.eq(PriceAdjustRoleItemRelatePO::getCompanyCode, req.getCompanyCode())
                    .eq(PriceAdjustRoleItemRelatePO::getRoleCode, AdjustRoleType.COMPANY_S_3)
                    .eq(PriceAdjustRoleItemRelatePO::getRoleType, AdjustRoleDimensionType.SUBMITTER);
            queryWrapper.eq(StrUtil.isNotEmpty(req.getOrCode()), PriceAdjustRoleItemRelatePO::getRoleData, req.getOrCode());

        }else {
            queryWrapper.eq(PriceAdjustRoleItemRelatePO::getRoleType, AdjustRoleDimensionType.APPROVER);
        }
        IPage<PriceAdjustRoleItemRelatePO> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        page = priceAdjustRoleItemRelateMapper.selectPage(page, queryWrapper);

        PageDTO<ToEditPageResp> pageDTO = new PageDTO<>(req.getCurrentPage(), req.getPageSize());
        pageDTO.setTotalCount(page.getTotal());
        pageDTO.setTotalPage(page.getPages());

        List<ToEditPageResp> list = new ArrayList<>();
        if (AdjustScopeType.COMPANY.name().equals(req.getScopeName())){
            for (PriceAdjustRoleItemRelatePO record : page.getRecords()) {
                ToEditPageResp resp = new ToEditPageResp();
                SysOrganizationDTO company = OrgInfoCacheUtils.getStoreListByCode(record.getCompanyCode());
                if (company != null) {
                    resp.setCompanyName(company.getOrName());
                    resp.setCompanyCode(company.getOrCode());
                }
                SysOrganizationDTO org = OrgInfoCacheUtils.getStoreListByCode(record.getRoleData());
                if (org != null) {
                    resp.setOrName(org.getOrName());
                    resp.setOrCode(org.getOrCode());
                    SysOrganizationDTO branch = OrgInfoCacheUtils.getBranchByOrCode(org.getOrCode());
                    if (branch != null) {
                        resp.setBranchName(branch.getOrName());
                        resp.setBranchCode(branch.getOrCode());
                    }
                }
                list.add(resp);
            }
        }else {
            for (PriceAdjustRoleItemRelatePO record : page.getRecords()) {
                ToEditPageResp resp = new ToEditPageResp();
                SysOrganizationDTO company = OrgInfoCacheUtils.getStoreListByCode(record.getCompanyCode());
                if (company != null) {
                    resp.setCompanyName(company.getOrName());
                    resp.setCompanyCode(company.getOrCode());
                }
                SysOrganizationDTO org = OrgInfoCacheUtils.getStoreListByCode(record.getRoleData());
                if (org != null) {
                    resp.setOrName(org.getOrName());
                    resp.setOrCode(org.getOrCode());
                }
                resp.setRoleType(AdjustRoleType.valueOf(record.getRoleCode()));
                list.add(resp);
            }
        }

        pageDTO.setData(list);
        return pageDTO;
    }

    @Override
    public List<RelateExtDTO> getItemRelateExtList(String code, AdjustRoleType roleType,List<String> orCodeList) {
        LambdaQueryWrapper<PriceAdjustRoleItemRelatePO> queryWrapper = new LambdaQueryWrapper<PriceAdjustRoleItemRelatePO>()
                .eq(PriceAdjustRoleItemRelatePO::getParentCode, code)
                .eq(PriceAdjustRoleItemRelatePO::getRoleCode, roleType);
        PriceAdjustRoleItemRelatePO priceAdjustRoleItemRelatePO = priceAdjustRoleItemRelateMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(priceAdjustRoleItemRelatePO)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PriceAdjustRoleItemRelateExtPO> relateExtQueryWrapper = new LambdaQueryWrapper<PriceAdjustRoleItemRelateExtPO>()
                .eq(PriceAdjustRoleItemRelateExtPO::getItemRelateId, priceAdjustRoleItemRelatePO.getId())
                .in(PriceAdjustRoleItemRelateExtPO::getOrCode, orCodeList);
        List<PriceAdjustRoleItemRelateExtPO> priceAdjustRoleItemRelateExtPOS = priceAdjustRoleItemRelateExtMapper.selectList(relateExtQueryWrapper);
        List<RelateExtDTO> relateExtDTOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(priceAdjustRoleItemRelateExtPOS)){
            for (PriceAdjustRoleItemRelateExtPO priceAdjustRoleItemRelateExtPO : priceAdjustRoleItemRelateExtPOS) {
                RelateExtDTO relateExtDTO = new RelateExtDTO();
                relateExtDTO.setRoleType(priceAdjustRoleItemRelateExtPO.getRoleType());
                relateExtDTO.setUserId(priceAdjustRoleItemRelateExtPO.getUserId());
                relateExtDTO.setOrCode(priceAdjustRoleItemRelateExtPO.getOrCode());
                relateExtDTOList.add(relateExtDTO);
            }
        }
        return relateExtDTOList;
    }

    @Override
    public List<ItemRelateApproverResp> getApproverItemList(String code) {
        LambdaQueryWrapper<PriceAdjustRoleItemRelatePO> queryWrapper = new LambdaQueryWrapper<PriceAdjustRoleItemRelatePO>().eq(PriceAdjustRoleItemRelatePO::getParentCode, code);
        List<PriceAdjustRoleItemRelatePO> poList = priceAdjustRoleItemRelateMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(poList)){
            return Collections.emptyList();
        }
        List<Long> roleRelateIdList = poList.stream().map(PriceAdjustRoleItemRelatePO::getId).collect(Collectors.toList());
        LambdaQueryWrapper<PriceAdjustRoleItemRelateExtPO> extWrapper = new LambdaQueryWrapper<PriceAdjustRoleItemRelateExtPO>().in(PriceAdjustRoleItemRelateExtPO::getItemRelateId, roleRelateIdList);
        List<PriceAdjustRoleItemRelateExtPO> extPOList = priceAdjustRoleItemRelateExtMapper.selectList(extWrapper);
        if (CollUtil.isEmpty(extPOList)){
            return Collections.emptyList();
        }
        Map<Long, List<PriceAdjustRoleItemRelateExtPO>> map = extPOList.stream().collect(Collectors.groupingBy(PriceAdjustRoleItemRelateExtPO::getItemRelateId));

        List<ItemRelateApproverResp> itemRelateApproverRespList = new ArrayList<>();
        for (PriceAdjustRoleItemRelatePO priceAdjustRoleItemRelatePO : poList) {
            ItemRelateApproverResp itemRelateApproverResp = new ItemRelateApproverResp();
            itemRelateApproverResp.setRoleCode(priceAdjustRoleItemRelatePO.getRoleCode());
            itemRelateApproverResp.setRoleName(AdjustRoleType.valueOf(priceAdjustRoleItemRelatePO.getRoleCode()).getNameCh());

            List<PriceAdjustRoleItemRelateExtPO> priceAdjustRoleItemRelateExtPOS = map.get(priceAdjustRoleItemRelatePO.getId());
            List<RelateExtReq> roleData = new ArrayList<>();
            if (CollUtil.isNotEmpty(priceAdjustRoleItemRelateExtPOS)){
                for (PriceAdjustRoleItemRelateExtPO extPO : priceAdjustRoleItemRelateExtPOS) {
                    RelateExtReq relateExtReq = new RelateExtReq();
                    BeanUtils.copyProperties(extPO, relateExtReq);
                    roleData.add(relateExtReq);
                }
            }
            itemRelateApproverResp.setRoleData(roleData);
            itemRelateApproverRespList.add(itemRelateApproverResp);
        }
        return itemRelateApproverRespList;
    }

    @Override
    public List<PriceAdjustRoleItemRelateBO> getRoleItemRelateByAppoverUserId(String userId) {
        PriceAdjustRoleItemRelateSearchPO searchPO = new PriceAdjustRoleItemRelateSearchPO();
        searchPO.setUserId(userId);
        List<PriceAdjustRoleItemRelateJoinExtPO>  poList = priceAdjustRoleItemRelateMapper.selectApproverJoinExt(searchPO);
        return BeanUtil.copyToList(poList, PriceAdjustRoleItemRelateBO.class);
    }

    @Override
    public AdjustRoleType getCurrentUserRoleType(String orCode) {
        LambdaQueryWrapper<PriceAdjustRoleItemRelatePO> queryWrapper = new LambdaQueryWrapper<PriceAdjustRoleItemRelatePO>()
                .eq(PriceAdjustRoleItemRelatePO::getRoleData, orCode)
                .eq(PriceAdjustRoleItemRelatePO::getRoleType, AdjustRoleDimensionType.SUBMITTER.name());
        PriceAdjustRoleItemRelatePO priceAdjustRoleItemRelatePO = priceAdjustRoleItemRelateMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(priceAdjustRoleItemRelatePO)){
            return AdjustRoleType.valueOf(priceAdjustRoleItemRelatePO.getRoleCode());
        }
        return null;
    }
}
