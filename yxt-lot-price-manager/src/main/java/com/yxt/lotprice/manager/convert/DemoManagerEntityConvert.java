package com.yxt.lotprice.manager.convert;

import cn.hutool.json.JSONUtil;
import com.yxt.lotprice.infrastructure.dao.po.DemoPO;
import com.yxt.lotprice.service.model.bo.DemoBO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.time.LocalDateTime;
import java.util.Date;

//@Component
@Mapper(componentModel = "spring",
        unmappedSourcePolicy = ReportingPolicy.IGNORE,unmappedTargetPolicy = ReportingPolicy.IGNORE,
        imports = {LocalDateTime.class, JSONUtil.class, Date.class})
public interface DemoManagerEntityConvert {

//    @Mapping(source = "po", target = "bo")
    DemoBO toDictBO(DemoPO demoPO);
}
