package com.yxt.lotprice.manager.convert;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.lang.dto.api.PageDTO;

public class PageUtil {


    public static <T> PageDTO<T> convertToPageDTO(IPage<T> iPage) {
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.setTotalCount(iPage.getTotal());
        pageDTO.setTotalPage(iPage.getPages());
        pageDTO.setData(iPage.getRecords());
        return pageDTO;
    }

    public static <T> PageDTO<T> convertToPageDTO(IPage<?> iPage, Class<T> t) {
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.setTotalCount(iPage.getTotal());
        pageDTO.setTotalPage(iPage.getPages());
        pageDTO.setData(BeanUtil.copyToList(iPage.getRecords(), t));
        return pageDTO;
    }
}
