package com.yxt.lotprice.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustFormItemDetailMapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustFormItemDetailPO;
import com.yxt.lotprice.service.cache.AllStoreInfoCache;
import com.yxt.lotprice.service.manager.iface.PriceAdjustFormItemDetailManager;
import com.yxt.lotprice.service.manager.sdk.dto.StoreDTO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemDetailBO;
import com.yxt.lotprice.service.model.enums.CancelStatusEnum;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * Since: 2025/05/22 9:52
 * Author: qs
 */
@Service
public class PriceAdjustFormItemDetailManagerImpl implements PriceAdjustFormItemDetailManager {

    @Resource
    private AllStoreInfoCache allStoreInfoCache;
    @Resource
    private PriceAdjustFormItemDetailMapper priceAdjustFormItemDetailMapper;

    @Override
    public List<PriceAdjustFormItemDetailBO> listEnableByStoreCodeErpCodes(PriceTypeEnum priceTypeEnum, String storeCode, Collection<String> erpCodes) {
        if (StringUtils.isEmpty(storeCode)) {
            throw new YxtBizException("门店编码为空");
        }
        // 获取门店所属分公司 从门店缓存获取
        StoreDTO store = allStoreInfoCache.getStoreCacheOrRemote(storeCode);
        if (store == null || StringUtils.isEmpty(store.getCompanyCode())) {
            throw new YxtBizException("门店或分公司编码不存在");
        }
        List<PriceAdjustFormItemDetailPO> priceAdjustFormItemDetailPOS = priceAdjustFormItemDetailMapper.selectList(new LambdaQueryWrapper<PriceAdjustFormItemDetailPO>()
                .eq(PriceAdjustFormItemDetailPO::getCompanyCode, store.getCompanyCode())
                .eq(PriceAdjustFormItemDetailPO::getStoreCode, storeCode)
                .in(PriceAdjustFormItemDetailPO::getErpCode, erpCodes)
                .eq(PriceAdjustFormItemDetailPO::getPriceType, priceTypeEnum)
                .eq(PriceAdjustFormItemDetailPO::getEnableStatus, CancelStatusEnum.NOT_CANCELED)
                .ge(PriceAdjustFormItemDetailPO::getEndTime, LocalDate.now())
        );

        return BeanUtil.copyToList(priceAdjustFormItemDetailPOS, PriceAdjustFormItemDetailBO.class);
    }

    @Override
    public boolean insertBatch(Collection<PriceAdjustFormItemDetailBO> records) {
        return priceAdjustFormItemDetailMapper.insertBatch(BeanUtil.copyToList(records, PriceAdjustFormItemDetailPO.class));
    }
}
