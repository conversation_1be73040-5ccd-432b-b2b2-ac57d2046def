package com.yxt.lotprice.manager.impl;

import com.yxt.lotprice.service.model.enums.redis.ExampleRedisKeyGroup;
import com.yxt.lotprice.infrastructure.dao.mapper.DemoMapper;
import com.yxt.lotprice.infrastructure.dao.po.DemoPO;
import com.yxt.lotprice.manager.convert.DemoManagerEntityConvert;
import com.yxt.lotprice.service.model.bo.DemoBO;
import com.yxt.lotprice.service.manager.iface.DemoManager;
import com.yxt.redis.cache.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Slf4j
@Service
public class DemoManagerImpl implements DemoManager {
    @Resource
    private DemoMapper demoMapper;
    @Resource
    private DemoManagerEntityConvert demoManagerEntityConvert;

    @Resource
    private RedisClient redisClient;

    @Override
    public DemoBO selectDictById(Integer id) {
        DemoPO demoPO = demoMapper.selectById(id);
        return demoManagerEntityConvert.toDictBO(demoPO);
    }

    @Override
    public DemoBO selectDictByIdFromRedis(Integer diseaseDictId) {
        DemoBO demoBO = selectDictById(diseaseDictId);
        redisClient.set(ExampleRedisKeyGroup.EXAMPLE_GROUP, "demoBO", demoBO);
        return redisClient.get(ExampleRedisKeyGroup.EXAMPLE_GROUP, "demoBO");
    }
}
