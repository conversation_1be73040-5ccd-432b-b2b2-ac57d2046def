<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-lot-price-manager</artifactId>
    <name>yxt-lot-price-manager</name>
    <description>yxt-lot-price-manager</description>
    <parent>
        <groupId>com.yxt.lotprice</groupId>
        <artifactId>yxt-lot-price</artifactId>
        <version>${reversion}</version>
    </parent>

    <properties>
        <module.deploy.skip>true</module.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yxt.lotprice</groupId>
            <artifactId>yxt-lot-price-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt.lotprice</groupId>
            <artifactId>yxt-lot-price-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt.lotprice</groupId>
            <artifactId>yxt-lot-price-message-sdk</artifactId>
        </dependency>

        <!-- elastic组件相关依赖 -->
        <!--
                <dependency>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                </dependency>

                <dependency>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </dependency>
        -->
        <!-- easy-es组件相关依赖 -->

        <!--
                        <dependency>
                            <groupId>org.dromara.easy-es</groupId>
                            <artifactId>easy-es-boot-starter</artifactId>
                        </dependency>
        -->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

</project>
